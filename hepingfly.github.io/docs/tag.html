<!DOCTYPE html>
<html data-color-mode="light" data-dark-theme="dark" data-light-theme="light" lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="content-type" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href='https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/Primer/21.0.7/primer.css' rel='stylesheet' />
    <script src='https://hepingfly.github.io/GmeekVercount.js'></script>
    <link rel="icon" href="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"><script>
        let theme = localStorage.getItem("meek_theme") || "light";
        document.documentElement.setAttribute("data-color-mode", theme);
    </script>
<meta name="description" content="和平自留地 search page">
<title>和平自留地 - Tag</title>

</head>
<style>
body{box-sizing: border-box;min-width: 200px;max-width: 900px;margin: 20px auto;padding: 45px;font-size: 16px;font-family: sans-serif;line-height: 1.25;}
#header{display:flex;padding-bottom:8px;border-bottom: 1px solid var(--borderColor-muted, var(--color-border-muted));margin-bottom: 16px;}
#footer {margin-top:64px; text-align: center;font-size: small;}

</style>

<style>
.tagTitle{margin:auto 0;font-size:40px;font-weight:bold;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.title-right{display:flex;margin:auto 0 0 auto;}
.title-right .circle{padding: 14px 16px;margin-right:8px;}

.subnav-search{width:222px;margin-top:8px;margin-right:8px;}
.subnav-search-input{width:160px;border-top-right-radius:0px;border-bottom-right-radius:0px;}
.subnav-search button{padding:5px 8px;border-top-left-radius:0px;border-bottom-left-radius:0px;}

.SideNav-icon{margin-right:16px}
.Label{color: #fff;margin-left:4px;}
#taglabel .Label {margin-bottom:8px;}
.Counter{color:#fff;background-color:rgba(234, 238, 242, 0.5)}

.genTime{float: right;}
.d-flex{min-width:0;}
.listTitle{overflow:hidden;white-space:nowrap;text-overflow: ellipsis;max-width: 100%;}
.listLabels{white-space:nowrap;}

@media (max-width: 600px) {
    body { padding: 8px;}
    .tagTitle{display:none;}
    .LabelTime{display:none;}
}
</style>


<body>
    <div id="header">
<span class="tagTitle"><span>Loading</span><span class="AnimatedEllipsis"></span></span>
<div class="title-right">
    <div class="subnav-search">
        <input type="search" class="form-control subnav-search-input float-left" aria-label="Search site" value="" style="height:32px;">
        <button class="btn float-left" type="submit" onclick="javascript:searchShow()">搜索</button>
        <svg class="subnav-search-icon octicon octicon-search" width="16" height="16" viewBox="0 0 16 16" aria-hidden="true">
            <path id="searchSVG" fill-rule="evenodd"></path>
        </svg>
    </div>
    <a href="https://hepingfly.github.io" id="buttonHome" class="btn btn-invisible circle" title="首页">
        <svg class="octicon" width="16" height="16">
            <path id="pathHome" fill-rule="evenodd"></path>
        </svg>
    </a>
    <a class="btn btn-invisible circle" onclick="modeSwitch()" title="切换主题">
        <svg class="octicon" width="16" height="16" >
            <path id="themeSwitch" fill-rule="evenodd"></path>
        </svg>
    </a>
</div>
</div>
    <div id="content">
<div id="taglabel" style="margin-bottom:8px;"></div>
<nav class="SideNav"></nav>
<div class="notFind" style="display:none;font-size:24px;margin:8px;">Not Find</div>
</div>
    <div id="footer"><div id="footer1">Copyright © <span id="copyrightYear"></span> <a href="https://hepingfly.github.io">和平自留地</a></div>
<div id="footer2">
    <span id="runday"></span><span>Powered by <a href="https://meekdai.com/Gmeek.html" target="_blank">Gmeek</a></span>
</div>

<script>
var now=new Date();
document.getElementById("copyrightYear").innerHTML=now.getFullYear();

if("06/24/2024"!=""){
    var startSite=new Date("06/24/2024");
    var diff=now.getTime()-startSite.getTime();
    var diffDay=Math.floor(diff/(1000*60*60*24));
    document.getElementById("runday").innerHTML="网站运行"+diffDay+"天"+" • ";
}
</script></div>
</body>
<script>
var IconList={'sun': 'M8 10.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zM8 12a4 4 0 100-8 4 4 0 000 8zM8 0a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0V.75A.75.75 0 018 0zm0 13a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5A.75.75 0 018 13zM2.343 2.343a.75.75 0 011.061 0l1.06 1.061a.75.75 0 01-1.06 1.06l-1.06-1.06a.75.75 0 010-1.06zm9.193 9.193a.75.75 0 011.06 0l1.061 1.06a.75.75 0 01-1.06 1.061l-1.061-1.06a.75.75 0 010-1.061zM16 8a.75.75 0 01-.75.75h-1.5a.75.75 0 010-1.5h1.5A.75.75 0 0116 8zM3 8a.75.75 0 01-.75.75H.75a.75.75 0 010-1.5h1.5A.75.75 0 013 8zm10.657-5.657a.75.75 0 010 1.061l-1.061 1.06a.75.75 0 11-1.06-1.06l1.06-1.06a.75.75 0 011.06 0zm-9.193 9.193a.75.75 0 010 1.06l-1.06 1.061a.75.75 0 11-1.061-1.06l1.06-1.061a.75.75 0 011.061 0z', 'moon': 'M9.598 1.591a.75.75 0 01.785-.175 7 7 0 11-8.967 8.967.75.75 0 01.961-.96 5.5 5.5 0 007.046-*********** 0 01.175-.786zm1.616 1.945a7 7 0 01-7.678 7.678 5.5 5.5 0 107.678-7.678z', 'sync': 'M1.705 8.005a.75.75 0 0 1 .834.656 5.5 5.5 0 0 0 9.592 2.97l-1.204-1.204a.25.25 0 0 1 .177-.427h3.646a.25.25 0 0 1 .25.25v3.646a.25.25 0 0 1-.427.177l-1.38-1.38A7.002 7.002 0 0 1 1.05 8.84a.75.75 0 0 1 .656-.834ZM8 2.5a5.487 5.487 0 0 0-4.131 1.869l1.204 1.204A.25.25 0 0 1 4.896 6H1.25A.25.25 0 0 1 1 5.75V2.104a.25.25 0 0 1 .427-.177l1.38 1.38A7.002 7.002 0 0 1 14.95 7.16a.75.75 0 0 1-1.49.178A5.5 5.5 0 0 0 8 2.5Z', 'home': 'M6.906.664a1.749 1.749 0 0 1 2.187 0l5.25 4.2c.415.332.657.835.657 1.367v7.019A1.75 1.75 0 0 1 13.25 15h-3.5a.75.75 0 0 1-.75-.75V9H7v5.25a.75.75 0 0 1-.75.75h-3.5A1.75 1.75 0 0 1 1 13.25V6.23c0-.531.242-1.034.657-1.366l5.25-4.2Zm1.25 1.171a.25.25 0 0 0-.312 0l-5.25 4.2a.25.25 0 0 0-.094.196v7.019c0 .138.112.25.25.25H5.5V8.25a.75.75 0 0 1 .75-.75h3.5a.75.75 0 0 1 .75.75v5.25h2.75a.25.25 0 0 0 .25-.25V6.23a.25.25 0 0 0-.094-.195Z', 'search': 'M15.7 13.3l-3.81-3.83A5.93 5.93 0 0 0 13 6c0-3.31-2.69-6-6-6S1 2.69 1 6s2.69 6 6 6c1.3 0 2.48-.41 3.47-1.11l3.83 3.81c.19.2.45.3.7.3.25 0 .52-.09.7-.3a.996.996 0 0 0 0-1.41v.01zM7 10.7c-2.59 0-4.7-2.11-4.7-4.7 0-2.59 2.11-4.7 4.7-4.7 2.59 0 4.7 2.11 4.7 4.7 0 2.59-2.11 4.7-4.7 4.7z', 'post': 'M0 3.75C0 2.784.784 2 1.75 2h12.5c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 14.25 14H1.75A1.75 1.75 0 0 1 0 12.25Zm1.75-.25a.25.25 0 0 0-.25.25v8.5c0 .138.112.25.25.25h12.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25ZM3.5 6.25a.75.75 0 0 1 .75-.75h7a.75.75 0 0 1 0 1.5h-7a.75.75 0 0 1-.75-.75Zm.75 2.25h4a.75.75 0 0 1 0 1.5h-4a.75.75 0 0 1 0-1.5Z'};
var utterancesLoad=0;

let themeSettings={
    "dark": ["dark","moon","#00f0ff","dark-blue"],
    "light": ["light","sun","#ff5000","github-light"],
    "auto": ["auto","sync","","preferred-color-scheme"]
};
function changeTheme(mode, icon, color, utheme){
    document.documentElement.setAttribute("data-color-mode",mode);
    document.getElementById("themeSwitch").setAttribute("d",value=IconList[icon]);
    document.getElementById("themeSwitch").parentNode.style.color=color;
    if(utterancesLoad==1){utterancesTheme(utheme);}
}
function modeSwitch(){
    let currentMode=document.documentElement.getAttribute('data-color-mode');
    let newMode = currentMode === "light" ? "dark" : currentMode === "dark" ? "auto" : "light";
    localStorage.setItem("meek_theme", newMode);
    if(themeSettings[newMode]){
        changeTheme(...themeSettings[newMode]);
    }
}
function utterancesTheme(theme){
    const message={type:'set-theme',theme: theme};
    const iframe=document.getElementsByClassName('utterances-frame')[0];
    iframe.contentWindow.postMessage(message,'https://utteranc.es');
}
if(themeSettings[theme]){changeTheme(...themeSettings[theme]);}
console.log("\n %c Gmeek last https://github.com/Meekdai/Gmeek \n","padding:5px 0;background:#02d81d;color:#fff");
</script>

<script>
document.getElementById("pathHome").setAttribute("d",IconList["home"]);
document.getElementById("searchSVG").setAttribute("d",IconList["search"]);

tagList=[];
labelsCount={};
jsonData='';
let requestJson="postList.json"
let request=new XMLHttpRequest();
request.open("GET",requestJson);
request.responseType='text';
request.send();
request.onload=function(){
    jsonData=JSON.parse(request.response);
    console.log(jsonData);
    showList(labelsCount);
    setClassDisplay(decodeURI(window.location.hash.slice(1)));
}

function showList(labelsCount){
    let SideNav=document.getElementsByClassName("SideNav")[0];
    SideNav.classList.add("border");
    let taglabel=document.getElementById("taglabel");

    jsonData['labelColorDict']["All"]="#000";
    labelsCount["All"]=0;
    for (let key in jsonData) {
        if (key !== 'labelColorDict' && Array.isArray(jsonData[key]['labels'])) {
            labelsCount["All"]++;
            for (let label of jsonData[key]['labels']) {
                labelsCount[label] = (labelsCount[label] || 0) + 1;
            }
        }
    }

    let sortedLabelsList = Object.keys(labelsCount).sort((a, b) => labelsCount[b] - labelsCount[a]);
    for (let label of sortedLabelsList) {
        tagList.push(label);
        let showLabels = document.createElement("button");
        showLabels.setAttribute("class", "Label");
        showLabels.setAttribute("style", "background-color:" + jsonData['labelColorDict'][label]+";padding:4px;");
        showLabels.innerHTML="&nbsp;&nbsp;"+label+" ";
        showLabels.setAttribute("onclick", "javascript:updateShowTag('" + label + "');");

        let LabelNum=document.createElement("span");
        LabelNum.setAttribute("class","Counter");
        LabelNum.innerHTML=labelsCount[label];
        showLabels.appendChild(LabelNum);
        taglabel.appendChild(showLabels);
    }

    for(i in jsonData){
        if(i!='labelColorDict'){
            let div=document.createElement("div");
            div.setAttribute("class","lists "+jsonData[i]['labels'].join(" "));
            let item=document.createElement("a");
            item.setAttribute("class","SideNav-item d-flex flex-items-center flex-justify-between");
            item.setAttribute("href",jsonData[i]['postUrl']);

            let center=document.createElement("div");
            center.setAttribute("class","d-flex flex-items-center");

            svg=document.createElementNS('http://www.w3.org/2000/svg','svg');
            path=document.createElementNS("http://www.w3.org/2000/svg","path"); 
            span=document.createElement("span");
            svg.setAttributeNS(null,"class","SideNav-icon octicon");
            svg.setAttributeNS(null,"style","width:16px;height:16px");
            path.setAttributeNS(null, "d", IconList["post"]);
            svg.appendChild(path);

            let title=document.createElement("span");
            title.setAttribute("class","listTitle");
            title.innerHTML=jsonData[i]['postTitle'];
            center.appendChild(svg);
            center.appendChild(title);

            let listLabels=document.createElement("div");
            listLabels.setAttribute("class","listLabels");
            
            for(label of jsonData[i]['labels']){
                let LabelName=document.createElement("span");
                LabelName.setAttribute("class","Label LabelName");
                LabelName.setAttribute("style","background-color:"+jsonData['labelColorDict'][label]);
                LabelName.innerHTML=label;
                listLabels.appendChild(LabelName);
            }
            let LabelTime=document.createElement("span");
            LabelTime.setAttribute("class","Label LabelTime");
            LabelTime.setAttribute("style","background-color:"+jsonData[i]['dateLabelColor']);
            LabelTime.innerHTML=jsonData[i]['createdDate'];
            listLabels.appendChild(LabelTime);

            item.appendChild(center);
            item.appendChild(listLabels);
            div.appendChild(item);
            SideNav.appendChild(div);
        }
    }
}

function updateShowTag(label){
    if(window.location.hash.slice(1)!=encodeURI(label)){
        window.location.hash="#"+(label);
        setClassDisplay(label);
    }
}

function setClassDisplay(label){
    let lists = document.getElementsByClassName("lists");
    let tagTitle = document.getElementsByClassName("tagTitle")[0];
    tagTitle.innerHTML="Tag #"+label;
    document.title=label+" - 和平自留地";
    document.getElementsByClassName("subnav-search-input")[0].value='';
    if(label=="All"){
        for(let i = 0; i < lists.length; i++){lists[i].style.display='block';}
        document.getElementsByClassName("notFind")[0].style.display='none';
    }
    else if(tagList.indexOf(label)!=-1){
        for(let i = 0; i < lists.length; i++){
            lists[i].style.display='none';
        }

        let labels = document.getElementsByClassName(label);
        for(let i = 0; i < labels.length; i++){
            labels[i].style.display='block';
        }
        document.getElementsByClassName("notFind")[0].style.display='none';
    }
    else{
        document.getElementsByClassName("subnav-search-input")[0].value=label;
        searchShow();
    }
}

function searchShow(){
    let lists = document.getElementsByClassName("lists");
    let tagTitle = document.getElementsByClassName("tagTitle")[0];
    let searchInput = document.getElementsByClassName("subnav-search-input")[0].value;
    tagTitle.innerHTML="Search #"+searchInput;
    if(searchInput==''){document.title="Search - 和平自留地";}
    else{document.title=searchInput+" - 和平自留地";}
    let a=0;
    window.location.hash="#"+(searchInput);
    for(let i = 0; i < lists.length; i++){
        if(lists[i].childNodes[0].childNodes[0].childNodes[1].innerHTML.toUpperCase().indexOf(searchInput.toUpperCase())==-1){lists[i].style.display='none';}
        else{lists[i].style.display='block';a=a+1;}
    }
    if(a==0){
        let notFind=document.getElementsByClassName("notFind")[0];
        notFind.style.display='block';
        notFind.innerHTML='Not Find "'+searchInput+'"';
    }
    else{document.getElementsByClassName("notFind")[0].style.display='none';}
}

</script>

</html>
