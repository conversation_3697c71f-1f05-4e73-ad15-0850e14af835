<!DOCTYPE html>
<html data-color-mode="light" data-dark-theme="dark" data-light-theme="light" lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="content-type" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href='https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/Primer/21.0.7/primer.css' rel='stylesheet' />
    <script src='https://hepingfly.github.io/GmeekVercount.js'></script>
    <link rel="icon" href="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"><script>
        let theme = localStorage.getItem("meek_theme") || "light";
        document.documentElement.setAttribute("data-color-mode", theme);
    </script>
<meta name="description" content="先提出一个问题：

视频号上现在那么多情感书单账号，那么这些情感书单账号可以做历史方向的赛道吗？

答案是：可以。">
<meta property="og:title" content="素人读书IP应该怎么做？">
<meta property="og:description" content="先提出一个问题：

视频号上现在那么多情感书单账号，那么这些情感书单账号可以做历史方向的赛道吗？

答案是：可以。">
<meta property="og:type" content="article">
<meta property="og:url" content="https://hepingfly.github.io/post/su-ren-du-shu-IP-ying-gai-zen-me-zuo-%EF%BC%9F.html">
<meta property="og:image" content="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG">
<title>素人读书IP应该怎么做？</title>



</head>
<style>
body{box-sizing: border-box;min-width: 200px;max-width: 900px;margin: 20px auto;padding: 45px;font-size: 16px;font-family: sans-serif;line-height: 1.25;}
#header{display:flex;padding-bottom:8px;border-bottom: 1px solid var(--borderColor-muted, var(--color-border-muted));margin-bottom: 16px;}
#footer {margin-top:64px; text-align: center;font-size: small;}

</style>

<style>
.postTitle{margin: auto 0;font-size:40px;font-weight:bold;}
.title-right{display:flex;margin:auto 0 0 auto;}
.title-right .circle{padding: 14px 16px;margin-right:8px;}
#postBody{border-bottom: 1px solid var(--color-border-default);padding-bottom:36px;}
#postBody hr{height:2px;}
#cmButton{height:48px;margin-top:48px;}
#comments{margin-top:64px;}
.g-emoji{font-size:24px;}
@media (max-width: 600px) {
    body {padding: 8px;}
    .postTitle{font-size:24px;}
}

</style>




<body>
    <div id="header">
<h1 class="postTitle">素人读书IP应该怎么做？</h1>
<div class="title-right">
    <a href="https://hepingfly.github.io" id="buttonHome" class="btn btn-invisible circle" title="首页">
        <svg class="octicon" width="16" height="16">
            <path id="pathHome" fill-rule="evenodd"></path>
        </svg>
    </a>
    

    <a class="btn btn-invisible circle" onclick="modeSwitch();" title="切换主题">
        <svg class="octicon" width="16" height="16" >
            <path id="themeSwitch" fill-rule="evenodd"></path>
        </svg>
    </a>

</div>
</div>
    <div id="content">
<div class="markdown-body" id="postBody"><p>先提出一个问题：</p>
<p>视频号上现在那么多情感书单账号，那么这些情感书单账号可以做历史方向的赛道吗？</p>
<p>答案是：可以。</p>
<p>比如 A 和 B 两个账号，都是用情感书单的方式在做账号，但是这两个账号的内核可能完全不一样。A账号虽然是在用情感书单的方式展现内容，但是他内容的内核可能是在揭露社会现实。B账号也是在用情感书单的方式展现内容，但是他内容的内核就仅仅是一些心灵鸡汤。</p>
<p>这样这两个账号所吸引来的人群可能完全不一样。虽然他们都是在用情感书单的方式去呈现内容。简单来说就是，披着情感书单的皮，但是内容内核完全不一样。</p>
<p>因此，内容决定方向，内容决定你吸引来的是什么人群。</p>
<p>第二个问题，如何定义读书账号？</p>
<p>此时，你想要做一个读书账号，那么账号里面的内容应该如何构成？如果你去找对标账号的话，找到很多读书类的账号，这些对标账号里面的内容可能差异很大，你不能摸到一个高赞的视频就抄到自己的账号，这样的话你账号里面的内容会很杂，很混乱，没有一条主线（一直没有一个明确的定位）。</p>
<p>举个例子：</p>
<p>有的账号内容定位，他发的内容是发现了一本好书，然后把书里面的金句读出来给用户听。这样该账号对用户的价值就是一个分享金句的账号，他看完你这些金句可能就走了，想让用户为你付费，很难。</p>
<p>有的账号的内容定位，他发的内容就是说一个历史故事，给用户讲历史故事，吸引用户停留、观看、涨粉。这样该账号对用户的价值就是一个故事号，用户看你的视频就是来听故事的，甚至可以打开你的视频一遍做家务一边听，等到你卖东西的时候，用户“唰”一下就走了，所以这样的账号内容，想要用户为你付费，很难。</p>
<p>综上，如果你的账号想要变现丝滑的话，那么一开始你就要告诉用户，你是一个推荐图书（卖书）的号。你账号的内容方向就要围绕着如何把一本书成功卖出去给用户这个方向去做。</p>
<p>那现在就有一个问题了，我该怎么组织内容呢？是今天推荐一本书，明天推荐一本书吗？这样你会发现你的内容还是很散，要怎么把它串起来呢？</p>
<p>方法一：</p>
<p>向用户传递价值观，吸引相同价值观的人。然后再去卖对应的书给用户。</p>
<p>比如，不管我视频内容怎么变化，形式怎么变化，但是</p>
<p>1）内容核心始终是，反民族主义，反宏大叙事。</p>
<p>2）内容核心始终是，爱国。中国怎么怎么好。</p>
<p>这样的话，你就会吸引跟你相同价值观的粉丝。到了一定程度之后，你就可以去卖类似价值观的产品给他们，比如说书籍。</p>
<p>但是有个注意点：</p>
<p>如果你自己的价值观和你视频里面的价值观不一样，为了做账号内容，你可以去硬凹这个价值观，初期可能会很别扭、难受。你是什么样的价值观，你就输出什么样的价值观，是最舒服的状态。</p>
<p>方法二：</p>
<p>你作为一个能够发现好书的达人，能够帮用户筛选出好书。用户不知道选哪本书比较好，你可以帮用户挑选出来，或者说有一些好书被埋没了，没有人知道，但是你把它又重新发掘出来，然后呈现给用户，这就是你能够给用户提供的价值。</p>
<p>如果选择这种方法，可以先从一个自己喜欢的方法，去输出内容，给用户推荐书籍，比如说，历史、军事、小说、散文、诗歌，哪个方向自己感兴趣就输出哪个方向的内容。</p></div>
<div style="font-size:small;margin-top:8px;float:right;">转载请注明出处</div>

<button class="btn btn-block" type="button" onclick="openComments()" id="cmButton">评论</button>
<div class="comments" id="comments"></div>

</div>
    <div id="footer"><div id="footer1">Copyright © <span id="copyrightYear"></span> <a href="https://hepingfly.github.io">和平自留地</a></div>
<div id="footer2">
    <span id="runday"></span><span>Powered by <a href="https://meekdai.com/Gmeek.html" target="_blank">Gmeek</a></span>
</div>

<script>
var now=new Date();
document.getElementById("copyrightYear").innerHTML=now.getFullYear();

if("06/24/2024"!=""){
    var startSite=new Date("06/24/2024");
    var diff=now.getTime()-startSite.getTime();
    var diffDay=Math.floor(diff/(1000*60*60*24));
    document.getElementById("runday").innerHTML="网站运行"+diffDay+"天"+" • ";
}
</script></div>
</body>
<script>
var IconList={'sun': 'M8 10.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zM8 12a4 4 0 100-8 4 4 0 000 8zM8 0a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0V.75A.75.75 0 018 0zm0 13a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5A.75.75 0 018 13zM2.343 2.343a.75.75 0 011.061 0l1.06 1.061a.75.75 0 01-1.06 1.06l-1.06-1.06a.75.75 0 010-1.06zm9.193 9.193a.75.75 0 011.06 0l1.061 1.06a.75.75 0 01-1.06 1.061l-1.061-1.06a.75.75 0 010-1.061zM16 8a.75.75 0 01-.75.75h-1.5a.75.75 0 010-1.5h1.5A.75.75 0 0116 8zM3 8a.75.75 0 01-.75.75H.75a.75.75 0 010-1.5h1.5A.75.75 0 013 8zm10.657-5.657a.75.75 0 010 1.061l-1.061 1.06a.75.75 0 11-1.06-1.06l1.06-1.06a.75.75 0 011.06 0zm-9.193 9.193a.75.75 0 010 1.06l-1.06 1.061a.75.75 0 11-1.061-1.06l1.06-1.061a.75.75 0 011.061 0z', 'moon': 'M9.598 1.591a.75.75 0 01.785-.175 7 7 0 11-8.967 8.967.75.75 0 01.961-.96 5.5 5.5 0 007.046-*********** 0 01.175-.786zm1.616 1.945a7 7 0 01-7.678 7.678 5.5 5.5 0 107.678-7.678z', 'sync': 'M1.705 8.005a.75.75 0 0 1 .834.656 5.5 5.5 0 0 0 9.592 2.97l-1.204-1.204a.25.25 0 0 1 .177-.427h3.646a.25.25 0 0 1 .25.25v3.646a.25.25 0 0 1-.427.177l-1.38-1.38A7.002 7.002 0 0 1 1.05 8.84a.75.75 0 0 1 .656-.834ZM8 2.5a5.487 5.487 0 0 0-4.131 1.869l1.204 1.204A.25.25 0 0 1 4.896 6H1.25A.25.25 0 0 1 1 5.75V2.104a.25.25 0 0 1 .427-.177l1.38 1.38A7.002 7.002 0 0 1 14.95 7.16a.75.75 0 0 1-1.49.178A5.5 5.5 0 0 0 8 2.5Z', 'home': 'M6.906.664a1.749 1.749 0 0 1 2.187 0l5.25 4.2c.415.332.657.835.657 1.367v7.019A1.75 1.75 0 0 1 13.25 15h-3.5a.75.75 0 0 1-.75-.75V9H7v5.25a.75.75 0 0 1-.75.75h-3.5A1.75 1.75 0 0 1 1 13.25V6.23c0-.531.242-1.034.657-1.366l5.25-4.2Zm1.25 1.171a.25.25 0 0 0-.312 0l-5.25 4.2a.25.25 0 0 0-.094.196v7.019c0 .138.112.25.25.25H5.5V8.25a.75.75 0 0 1 .75-.75h3.5a.75.75 0 0 1 .75.75v5.25h2.75a.25.25 0 0 0 .25-.25V6.23a.25.25 0 0 0-.094-.195Z', 'github': 'M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z'};
var utterancesLoad=0;

let themeSettings={
    "dark": ["dark","moon","#00f0ff","dark-blue"],
    "light": ["light","sun","#ff5000","github-light"],
    "auto": ["auto","sync","","preferred-color-scheme"]
};
function changeTheme(mode, icon, color, utheme){
    document.documentElement.setAttribute("data-color-mode",mode);
    document.getElementById("themeSwitch").setAttribute("d",value=IconList[icon]);
    document.getElementById("themeSwitch").parentNode.style.color=color;
    if(utterancesLoad==1){utterancesTheme(utheme);}
}
function modeSwitch(){
    let currentMode=document.documentElement.getAttribute('data-color-mode');
    let newMode = currentMode === "light" ? "dark" : currentMode === "dark" ? "auto" : "light";
    localStorage.setItem("meek_theme", newMode);
    if(themeSettings[newMode]){
        changeTheme(...themeSettings[newMode]);
    }
}
function utterancesTheme(theme){
    const message={type:'set-theme',theme: theme};
    const iframe=document.getElementsByClassName('utterances-frame')[0];
    iframe.contentWindow.postMessage(message,'https://utteranc.es');
}
if(themeSettings[theme]){changeTheme(...themeSettings[theme]);}
console.log("\n %c Gmeek last https://github.com/Meekdai/Gmeek \n","padding:5px 0;background:#02d81d;color:#fff");
</script>

<script>
document.getElementById("pathHome").setAttribute("d",IconList["home"]);




function openComments(){
    cm=document.getElementById("comments");
    cmButton=document.getElementById("cmButton");
    cmButton.innerHTML="loading";
    span=document.createElement("span");
    span.setAttribute("class","AnimatedEllipsis");
    cmButton.appendChild(span);

    script=document.createElement("script");
    script.setAttribute("src","https://utteranc.es/client.js");
    script.setAttribute("repo","hepingfly/hepingfly.github.io");
    script.setAttribute("issue-term","title");
    
    if(localStorage.getItem("meek_theme")=="dark"){script.setAttribute("theme","dark-blue");}
    else if(localStorage.getItem("meek_theme")=="light") {script.setAttribute("theme","github-light");}
    else{script.setAttribute("theme","preferred-color-scheme");}
    
    script.setAttribute("crossorigin","anonymous");
    script.setAttribute("async","");
    cm.appendChild(script);

    int=self.setInterval("iFrameLoading()",200);
}

function iFrameLoading(){
    var utterances=document.getElementsByClassName('utterances');
    if(utterances.length==1){
        if(utterances[0].style.height!=""){
            utterancesLoad=1;
            int=window.clearInterval(int);
            document.getElementById("cmButton").style.display="none";
            console.log("utterances Load OK");
        }
    }
}



</script>
<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>

</html>
