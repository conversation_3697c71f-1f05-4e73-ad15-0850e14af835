<!DOCTYPE html>
<html data-color-mode="light" data-dark-theme="dark" data-light-theme="light" lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="content-type" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href='https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/Primer/21.0.7/primer.css' rel='stylesheet' />
    <script src='https://hepingfly.github.io/GmeekVercount.js'></script>
    <link rel="icon" href="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"><script>
        let theme = localStorage.getItem("meek_theme") || "light";
        document.documentElement.setAttribute("data-color-mode", theme);
    </script>
<meta name="description" content="## 1、前言

**个人品牌有什么用？**

1、帮你快速获取机会

2、帮你吸引人脉

一句话概括，个人品牌可以放大自己的势能，主动吸引人脉获取机会。">
<meta property="og:title" content="每个人都可以有自己的个人品牌">
<meta property="og:description" content="## 1、前言

**个人品牌有什么用？**

1、帮你快速获取机会

2、帮你吸引人脉

一句话概括，个人品牌可以放大自己的势能，主动吸引人脉获取机会。">
<meta property="og:type" content="article">
<meta property="og:url" content="https://hepingfly.github.io/post/mei-ge-ren-du-ke-yi-you-zi-ji-de-ge-ren-pin-pai.html">
<meta property="og:image" content="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG">
<title>每个人都可以有自己的个人品牌</title>



</head>
<style>
body{box-sizing: border-box;min-width: 200px;max-width: 900px;margin: 20px auto;padding: 45px;font-size: 16px;font-family: sans-serif;line-height: 1.25;}
#header{display:flex;padding-bottom:8px;border-bottom: 1px solid var(--borderColor-muted, var(--color-border-muted));margin-bottom: 16px;}
#footer {margin-top:64px; text-align: center;font-size: small;}

</style>

<style>
.postTitle{margin: auto 0;font-size:40px;font-weight:bold;}
.title-right{display:flex;margin:auto 0 0 auto;}
.title-right .circle{padding: 14px 16px;margin-right:8px;}
#postBody{border-bottom: 1px solid var(--color-border-default);padding-bottom:36px;}
#postBody hr{height:2px;}
#cmButton{height:48px;margin-top:48px;}
#comments{margin-top:64px;}
.g-emoji{font-size:24px;}
@media (max-width: 600px) {
    body {padding: 8px;}
    .postTitle{font-size:24px;}
}

</style>
<style>.markdown-alert{padding:0.5rem 1rem;margin-bottom:1rem;border-left:.25em solid var(--borderColor-default,var(--color-border-default));}.markdown-alert .markdown-alert-title {display:flex;font-weight:var(--base-text-weight-medium,500);align-items:center;line-height:1;}.markdown-alert>:first-child {margin-top:0;}.markdown-alert>:last-child {margin-bottom:0;}</style><style>.markdown-alert.markdown-alert-important {border-left-color:var(--borderColor-done-emphasis, var(--color-done-emphasis));background-color:var(--color-done-subtle);}.markdown-alert.markdown-alert-important .markdown-alert-title {color: var(--fgColor-done,var(--color-done-fg));}</style>



<body>
    <div id="header">
<h1 class="postTitle">每个人都可以有自己的个人品牌</h1>
<div class="title-right">
    <a href="https://hepingfly.github.io" id="buttonHome" class="btn btn-invisible circle" title="首页">
        <svg class="octicon" width="16" height="16">
            <path id="pathHome" fill-rule="evenodd"></path>
        </svg>
    </a>
    

    <a class="btn btn-invisible circle" onclick="modeSwitch();" title="切换主题">
        <svg class="octicon" width="16" height="16" >
            <path id="themeSwitch" fill-rule="evenodd"></path>
        </svg>
    </a>

</div>
</div>
    <div id="content">
<div class="markdown-body" id="postBody"><h2>1、前言</h2>
<p><strong>个人品牌有什么用？</strong></p>
<p>1、帮你快速获取机会</p>
<p>2、帮你吸引人脉</p>
<p>一句话概括，个人品牌可以放大自己的势能，主动吸引人脉获取机会。</p>
<h2>2、个人品牌好处</h2>
<h3>2.1帮你快速获取机会</h3>
<p>如果你在工作中给别人形成的印象是负责、靠谱。那么在有晋升机会的时候，领导会更倾向把这个机会让给你。因为你负责、靠谱这个个人品牌会让领导觉得把事情交给你，他放心。</p>
<h3>2.2 帮你吸引人脉</h3>
<p>比如你现在运营一个公众号，每天都在公众号上分享哪里比较好玩，各种有用的攻略。随着你不断地向用户传递出他很会玩这一个信息，那么你就成功的给自己建设了一个个人品牌，叫「最会玩的女孩」</p>
<p>这个会玩的个人品牌，就会使得很多陌生人来找你。甚至还有人付费请教你某某时间点去哪里旅游比较好，等等。这样半年或者一年的时间，你就可能会赚到一笔不菲的收入。</p>
<p>所以综上：</p>
<div class="markdown-alert markdown-alert-important"><p class="markdown-alert-title"><svg class="octicon octicon-report mr-2" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .138.112.25.25.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>Important</p><p>个人品牌塑造好了，你的能力就可以最大的被别人给看到、想到、用到，主动吸引到你可能都想不到的人脉。</p>
</div>
<h2>3、什么是个人品牌</h2>
<p>在营销中有一个很重要的东西，叫**「第一反应品牌」**</p>
<p>意思就是当你想买某样东西的时候，你第一个想起来的品牌。</p>
<p>比如说：</p>
<p>可乐，一定想到的是可口可乐/百事可乐</p>
<p>去屑洗发水，想到的是海飞丝</p>
<p>凉茶，一定想到的是王老吉/加多宝</p>
<p>这些品牌通过大量的广告和一些营销手段，不断的固话你的单一认知，在你的脑海里形成了一个深刻的印象，从而就变成了第一反应品牌。</p>
<p>类似的，人也需要品牌，就是我们说的个人品牌。</p>
<p><strong>个人品牌</strong></p>
<p>通俗点说就是，当别人想到你的时候，对你的第一反应。也就是你的第一关键词。</p>
<p>比如中国演喜剧最好的男演员是谁？此时你的脑海中应该有一些演员的名字。</p>
<p>比如，你的同事小李是一个什么样的人？你的脑海中可能浮现，喜欢加班、不爱说话，技术好，那么这些关键词就是他的个人品牌。</p>
<p>很多人在日常生活中没有营销自己的意识，不会主动创造自己给别人的第一关键词。比如你现在去找你身边的人评价一下你，如果他说的很含糊，就说明你没有形成自己的个人品牌。</p>
<div class="markdown-alert markdown-alert-important"><p class="markdown-alert-title"><svg class="octicon octicon-report mr-2" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .138.112.25.25.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>Important</p><p>你想传递给别人什么信息，想给别人留下什么样的印象，就应该用日常的方式去传递出这个信息，这个你的特质可以精准的被他人捕捉和理解</p>
</div>
<p>举例：</p>
<p>你要让别人觉得你是一个优雅的人</p>
<p>那么你说话、穿衣、做事，你生活中的日常用品，面对愤怒时处理情绪的方法，都要体现出优雅这个特质。久而久之，别人想到优雅，就会想到你。</p>
<h2>4、建立个人品牌的方法</h2>
<p>上面说到个人品牌就是别人对你的第一关键词，所以你想要建立自己的个人品牌，你就要想自己的关键词是什么？</p>
<div class="markdown-alert markdown-alert-important"><p class="markdown-alert-title"><svg class="octicon octicon-report mr-2" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .138.112.25.25.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>Important</p><p>个人品牌的顶层设计 = 提炼关键词</p>
</div>
<p>如何提炼关键词？</p>
<p>1、写出自己的 30 个关键词</p>
<p>如果你觉得 30 个关键词自己写太多了，你也可以去采访一下你身边的人，搜集一下他们对你的评价，然后把这些词都写在一张白纸上。</p>
<p>2、筛选关键词</p>
<p>1）选出对自己是可以长期有价值的东西，把非长期、没有价值的关键词删掉</p>
<p>2）选出这些关键词里面对他人有价值的东西</p>
<p>3）选出可被外化表现的关键词（比如：心地善良，他没法被外化，所以他就不能作为你第一品牌的关键词）</p>
<p>经过上面 3 层筛选，剩下的就是你的关键词。</p>
<p>举例：</p>
<div class="markdown-alert markdown-alert-important"><p class="markdown-alert-title"><svg class="octicon octicon-report mr-2" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .138.112.25.25.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>Important</p><p>你的关键词是：</p>
<p>好看、有品味、有爱心、会吃美食、逻辑好、 学习能力强</p>
<p>那么好看、逻辑好、学习能力强，就是对你<strong>长期有价值的关键词</strong></p>
<p>有品位、会吃美食，就是<strong>对别人有价值</strong>的关键词（别人想买有品味的东西时，就会询问你的意见。别人想找好吃的餐厅也会来询问你的意见。）</p>
<p>像有爱心，这个就是<strong>比较难外化表现</strong>的关键词（因为不认识你的人，是感知不到你有没有爱心的）</p>
</div>
<p><strong>提炼完关键词后，如何让关键词变成你的个人品牌呢？</strong></p>
<p>你想让别人感知到你是一个什么样的人，第一时间肯定是从你的穿着、表达和做的事情去感知的。</p>
<p>所以塑造个人品牌就是三方面：</p>
<div class="markdown-alert markdown-alert-important"><p class="markdown-alert-title"><svg class="octicon octicon-report mr-2" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .138.112.25.25.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>Important</p><p>个人品牌的落地 = 穿什么 + 说什么 + 干什么</p>
</div>
<p>1、穿什么</p>
<p>穿衣服你需要知道 3 点</p>
<div class="markdown-alert markdown-alert-important"><p class="markdown-alert-title"><svg class="octicon octicon-report mr-2" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .138.112.25.25.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>Important</p><p>1、穿衣服的本质，是让别人认识你的工具</p>
<p>2、穿衣服最重要的事考虑场合</p>
<p>3、你可以根据兴趣，创造你的标志性形象</p>
</div>
<p>先说第一条：</p>
<p>关于穿什么可能会有人去反驳，像马云这样很厉害的企业家，根本不注意穿什么衣服，厉害的人都不在意外表的穿着。</p>
<p>这个观点其实是错误的。因为马云这样的人，他有很明确的个人标签，他是阿里的创始人。你认识马云肯定不是因为它穿了什么衣服，而是他事业上的成功。所以当大家对他已经有一个鲜明的认知了，那么这个人穿什么衣服也就不重要了。</p>
<p>当一个没有被大众认知标签的普通人，如何被别人辨识呢？</p>
<p>在别人对你一无所知的情况下，视觉信息肯定是最重要的。这就是为什么中介、律师他要穿西装打领带，因为他要建立一个可靠、专业的形象。</p>
<p>所以如果你想要建立起别人对你的印象，你的穿衣风格一定要跟你的个人形象保持一致。假如一个穿着大裤衩的律师来和你谈案子，那么你肯定会怀疑他的专业性。</p>
<p>再说第二条：</p>
<p>比如说你今天去参加一个晚宴，所有人都穿礼服，你非要穿个短袖，你到了现场之后一定会不自在。再比如去沙滩，所有人都穿比基尼、短袖、短裤，你穿个高跟鞋和礼服，同样会不自在。你的穿着和当下的场合不能融合。</p>
<p>再说第三条：</p>
<p>如果说你对自己的穿衣风格有兴趣，你还可以去创造自己标志性的形象。因为**一个标志性的形象，会比较容易超越其他认知，称为一个迅速传播的记忆点。**比如说有的人喜欢带墨镜，别人可能跟你不熟，但是提到你可能就会想到你的墨镜。</p></div>
<div style="font-size:small;margin-top:8px;float:right;">转载请注明出处</div>

<button class="btn btn-block" type="button" onclick="openComments()" id="cmButton">评论</button>
<div class="comments" id="comments"></div>

</div>
    <div id="footer"><div id="footer1">Copyright © <span id="copyrightYear"></span> <a href="https://hepingfly.github.io">和平自留地</a></div>
<div id="footer2">
    <span id="runday"></span><span>Powered by <a href="https://meekdai.com/Gmeek.html" target="_blank">Gmeek</a></span>
</div>

<script>
var now=new Date();
document.getElementById("copyrightYear").innerHTML=now.getFullYear();

if("06/24/2024"!=""){
    var startSite=new Date("06/24/2024");
    var diff=now.getTime()-startSite.getTime();
    var diffDay=Math.floor(diff/(1000*60*60*24));
    document.getElementById("runday").innerHTML="网站运行"+diffDay+"天"+" • ";
}
</script></div>
</body>
<script>
var IconList={'sun': 'M8 10.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zM8 12a4 4 0 100-8 4 4 0 000 8zM8 0a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0V.75A.75.75 0 018 0zm0 13a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5A.75.75 0 018 13zM2.343 2.343a.75.75 0 011.061 0l1.06 1.061a.75.75 0 01-1.06 1.06l-1.06-1.06a.75.75 0 010-1.06zm9.193 9.193a.75.75 0 011.06 0l1.061 1.06a.75.75 0 01-1.06 1.061l-1.061-1.06a.75.75 0 010-1.061zM16 8a.75.75 0 01-.75.75h-1.5a.75.75 0 010-1.5h1.5A.75.75 0 0116 8zM3 8a.75.75 0 01-.75.75H.75a.75.75 0 010-1.5h1.5A.75.75 0 013 8zm10.657-5.657a.75.75 0 010 1.061l-1.061 1.06a.75.75 0 11-1.06-1.06l1.06-1.06a.75.75 0 011.06 0zm-9.193 9.193a.75.75 0 010 1.06l-1.06 1.061a.75.75 0 11-1.061-1.06l1.06-1.061a.75.75 0 011.061 0z', 'moon': 'M9.598 1.591a.75.75 0 01.785-.175 7 7 0 11-8.967 8.967.75.75 0 01.961-.96 5.5 5.5 0 007.046-*********** 0 01.175-.786zm1.616 1.945a7 7 0 01-7.678 7.678 5.5 5.5 0 107.678-7.678z', 'sync': 'M1.705 8.005a.75.75 0 0 1 .834.656 5.5 5.5 0 0 0 9.592 2.97l-1.204-1.204a.25.25 0 0 1 .177-.427h3.646a.25.25 0 0 1 .25.25v3.646a.25.25 0 0 1-.427.177l-1.38-1.38A7.002 7.002 0 0 1 1.05 8.84a.75.75 0 0 1 .656-.834ZM8 2.5a5.487 5.487 0 0 0-4.131 1.869l1.204 1.204A.25.25 0 0 1 4.896 6H1.25A.25.25 0 0 1 1 5.75V2.104a.25.25 0 0 1 .427-.177l1.38 1.38A7.002 7.002 0 0 1 14.95 7.16a.75.75 0 0 1-1.49.178A5.5 5.5 0 0 0 8 2.5Z', 'home': 'M6.906.664a1.749 1.749 0 0 1 2.187 0l5.25 4.2c.415.332.657.835.657 1.367v7.019A1.75 1.75 0 0 1 13.25 15h-3.5a.75.75 0 0 1-.75-.75V9H7v5.25a.75.75 0 0 1-.75.75h-3.5A1.75 1.75 0 0 1 1 13.25V6.23c0-.531.242-1.034.657-1.366l5.25-4.2Zm1.25 1.171a.25.25 0 0 0-.312 0l-5.25 4.2a.25.25 0 0 0-.094.196v7.019c0 .138.112.25.25.25H5.5V8.25a.75.75 0 0 1 .75-.75h3.5a.75.75 0 0 1 .75.75v5.25h2.75a.25.25 0 0 0 .25-.25V6.23a.25.25 0 0 0-.094-.195Z', 'github': 'M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z'};
var utterancesLoad=0;

let themeSettings={
    "dark": ["dark","moon","#00f0ff","dark-blue"],
    "light": ["light","sun","#ff5000","github-light"],
    "auto": ["auto","sync","","preferred-color-scheme"]
};
function changeTheme(mode, icon, color, utheme){
    document.documentElement.setAttribute("data-color-mode",mode);
    document.getElementById("themeSwitch").setAttribute("d",value=IconList[icon]);
    document.getElementById("themeSwitch").parentNode.style.color=color;
    if(utterancesLoad==1){utterancesTheme(utheme);}
}
function modeSwitch(){
    let currentMode=document.documentElement.getAttribute('data-color-mode');
    let newMode = currentMode === "light" ? "dark" : currentMode === "dark" ? "auto" : "light";
    localStorage.setItem("meek_theme", newMode);
    if(themeSettings[newMode]){
        changeTheme(...themeSettings[newMode]);
    }
}
function utterancesTheme(theme){
    const message={type:'set-theme',theme: theme};
    const iframe=document.getElementsByClassName('utterances-frame')[0];
    iframe.contentWindow.postMessage(message,'https://utteranc.es');
}
if(themeSettings[theme]){changeTheme(...themeSettings[theme]);}
console.log("\n %c Gmeek last https://github.com/Meekdai/Gmeek \n","padding:5px 0;background:#02d81d;color:#fff");
</script>

<script>
document.getElementById("pathHome").setAttribute("d",IconList["home"]);




function openComments(){
    cm=document.getElementById("comments");
    cmButton=document.getElementById("cmButton");
    cmButton.innerHTML="loading";
    span=document.createElement("span");
    span.setAttribute("class","AnimatedEllipsis");
    cmButton.appendChild(span);

    script=document.createElement("script");
    script.setAttribute("src","https://utteranc.es/client.js");
    script.setAttribute("repo","hepingfly/hepingfly.github.io");
    script.setAttribute("issue-term","title");
    
    if(localStorage.getItem("meek_theme")=="dark"){script.setAttribute("theme","dark-blue");}
    else if(localStorage.getItem("meek_theme")=="light") {script.setAttribute("theme","github-light");}
    else{script.setAttribute("theme","preferred-color-scheme");}
    
    script.setAttribute("crossorigin","anonymous");
    script.setAttribute("async","");
    cm.appendChild(script);

    int=self.setInterval("iFrameLoading()",200);
}

function iFrameLoading(){
    var utterances=document.getElementsByClassName('utterances');
    if(utterances.length==1){
        if(utterances[0].style.height!=""){
            utterancesLoad=1;
            int=window.clearInterval(int);
            document.getElementById("cmButton").style.display="none";
            console.log("utterances Load OK");
        }
    }
}



</script>
<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>

</html>
