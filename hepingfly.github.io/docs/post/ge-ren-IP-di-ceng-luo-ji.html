<!DOCTYPE html>
<html data-color-mode="light" data-dark-theme="dark" data-light-theme="light" lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="content-type" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href='https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/Primer/21.0.7/primer.css' rel='stylesheet' />
    <script src='https://hepingfly.github.io/GmeekVercount.js'></script>
    <link rel="icon" href="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"><script>
        let theme = localStorage.getItem("meek_theme") || "light";
        document.documentElement.setAttribute("data-color-mode", theme);
    </script>
<meta name="description" content="想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。">
<meta property="og:title" content="个人IP底层逻辑">
<meta property="og:description" content="想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。">
<meta property="og:type" content="article">
<meta property="og:url" content="https://hepingfly.github.io/post/ge-ren-IP-di-ceng-luo-ji.html">
<meta property="og:image" content="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG">
<title>个人IP底层逻辑</title>



</head>
<style>
body{box-sizing: border-box;min-width: 200px;max-width: 900px;margin: 20px auto;padding: 45px;font-size: 16px;font-family: sans-serif;line-height: 1.25;}
#header{display:flex;padding-bottom:8px;border-bottom: 1px solid var(--borderColor-muted, var(--color-border-muted));margin-bottom: 16px;}
#footer {margin-top:64px; text-align: center;font-size: small;}

</style>

<style>
.postTitle{margin: auto 0;font-size:40px;font-weight:bold;}
.title-right{display:flex;margin:auto 0 0 auto;}
.title-right .circle{padding: 14px 16px;margin-right:8px;}
#postBody{border-bottom: 1px solid var(--color-border-default);padding-bottom:36px;}
#postBody hr{height:2px;}
#cmButton{height:48px;margin-top:48px;}
#comments{margin-top:64px;}
.g-emoji{font-size:24px;}
@media (max-width: 600px) {
    body {padding: 8px;}
    .postTitle{font-size:24px;}
}

</style>




<body>
    <div id="header">
<h1 class="postTitle">个人IP底层逻辑</h1>
<div class="title-right">
    <a href="https://hepingfly.github.io" id="buttonHome" class="btn btn-invisible circle" title="首页">
        <svg class="octicon" width="16" height="16">
            <path id="pathHome" fill-rule="evenodd"></path>
        </svg>
    </a>
    

    <a class="btn btn-invisible circle" onclick="modeSwitch();" title="切换主题">
        <svg class="octicon" width="16" height="16" >
            <path id="themeSwitch" fill-rule="evenodd"></path>
        </svg>
    </a>

</div>
</div>
    <div id="content">
<div class="markdown-body" id="postBody"><p>想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。</p>
<p><a target="_blank" rel="noopener noreferrer nofollow" href="https://camo.githubusercontent.com/5a1e527fcf1532ecb4b8a809b496008edca52f021c8781db53112f4648730980/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f686570696e67666c792f696d61676573406d61696e2f25453725423425413025453425424125424169702545392539332542452e706e67"><img src="https://camo.githubusercontent.com/5a1e527fcf1532ecb4b8a809b496008edca52f021c8781db53112f4648730980/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f686570696e67666c792f696d61676573406d61696e2f25453725423425413025453425424125424169702545392539332542452e706e67" data-canonical-src="https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E7%B4%A0%E4%BA%BAip%E9%93%BE.png" style="max-width: 100%;"></a></p>
<p>做 IP 首先要思考的几件事：</p>
<p>1、自己的目标人群是什么？这部分人群画像是什么？</p>
<p>补充：年龄只是人群画像其中一个维度，不能一说到目标人群就是 XXX 岁，还有其他维度比如很重视健康的人群、很焦虑的人群、有/无孩子的人群</p>
<p>2、这部分人（你的目标人群）的需求是什么？</p>
<p>想明白这两件事之后，我们要做的就是根据上面这两个问题给出解决方案。解决方案可以是产品也可以是服务。</p>
<p>有了解决方案之后，我们要把这个解决方案给呈现出来，呈现到用户面前去吸引用户，然后去做转化。</p>
<p><strong>注：</strong></p>
<blockquote>
<p>做IP 一定要围绕目标人群去做。</p>
<p>比如你的人群是中老年人群，那你发的内容就不能一会吸引中老年群体，一会又去吸引宝妈群体，这样系统无法辨认，无法给你准确推流。</p>
</blockquote>
<p>以「国之脊梁」这本书为例，来分析一下这个逻辑：</p>
<p>比如我们现在发现很多爆款视频，都在带《国之脊梁》这本书。当我刷到这个爆款视频，假设我是做历史赛道的，那么现在问题来了，这个爆款视频还有这个品我能不能跟？</p>
<blockquote>
<p>回答这个问题，首先你需要思考：你面向的是什么人群？你解决的是什么需求？</p>
<p>只有这个爆款视频面向的人群和你的目标人群一致，解决的需求和你的账号属性匹配，这时候才适合你去跟。</p>
<p>所以我们可以去挖《国之脊梁》这本书它的爆款文案，<strong>解决了什么需求？</strong>（自己判断）还有<strong>面向的人群</strong>是什么？（可以用巨量百应去看）</p>
<p>人群：宝妈，家里有小孩子的</p>
<p>需求：暑假到了，和孩子一起看一本书（育儿）</p>
<p>解决方案：</p>
<p>找一些榜样，给孩子树立榜样。所以宝妈在教育孩子的时候就有了谈资，教育孩子的谈资。告诉你一些榜样的故事，宝妈可以拿着这些故事去教育孩子。</p>
<p>呈现形式：混剪名人视频、口播</p>
<p>转化：告诉你我上面说的这些榜样故事，在这本书里面都有，你把这本书买回去，你就可以和你的孩子一起看，来教育孩子。</p>
</blockquote>
<p><strong>思考：</strong></p>
<p>围绕这一整个 IP 链，有哪些地方可以调整？</p>
<p>==改变解决方案==</p>
<p>例1：</p>
<p>我就针对当前一样的人群，一样的需求，去一直做不同的解决方案。</p>
<p>比如围绕宝妈需要教育孩子，陪着孩子一起看书这个需求，爆款视频是一起看《国之脊梁》这本书，现在我给出不同的解决方案，这时候我就可以拓宽品类了，去带一些《漫画版王阳明》《规矩》等等品。</p>
<p>接下来就去摸清楚，用什么样的呈现形式把这些解决方案给呈现出来。（呈现形式需要不断迭代...）</p>
<p><code class="notranslate">慢慢做着做着就会让用户形成一种认知，我想要找育儿的解决方法，那么我直接来找这个账号就好了，至此 IP 就成了</code>。</p>
<p>例2：</p>
<p>针对当前一样的人群，一样的需求，同一个解决方案（但是围绕这个解决方案去做不同的内容）</p>
<p>比如上面《国之脊梁》这本书，他用的是 AB 两个榜样，那我就可以换成 CD 两个榜样用现有的爆款文案框架再做一遍，换汤不换药。</p>
<p><strong>注：</strong></p>
<p>这个解决方案，如果你有商品通过商品去帮用户解决，那么你就带货。如果没有商品，那么你给出的解决方案就用来积累信任，积累影响力，积累账号权重。</p>
<p>==改变呈现方式==</p>
<p>例：</p>
<p>我就针对当前一样的人群，一样的需求，一样的解决方案，去做内容形式的改变</p>
<p>a.爆款视频采用的是混剪方式，那么我们就可以把它改成真人出镜口播的方式</p>
<p>b.别人用真人口播，那么我真人口播 + 黄金 3 秒吸睛 + 服化道到位</p>
<p>c.别人真人口播 + 黄金 3 秒吸睛 + 服化道到位，那么我找一个年纪大一点的老外来出镜口播</p>
<blockquote>
<p>a.做 IP 过程中有一个问题是始终要思考的，就是用户为什么要关注你？</p>
<p>b.每一个爆品（可以在抖音巨量百应看到）背后都有对应的人群和需求</p>
<p>我们就可以去挖掘这个爆品背后的需求</p>
<p>c.如果想要做一个有生命力的 IP ，是确确实实需要去研究这个人群，去懂这个人群的。</p>
<p>你如果有几千个信任你这个 IP 的人，那么一年变现几十万是不成问题的。</p>
</blockquote>
<p>案例：</p>
<p>比如现在的目标人群是一群非常内耗的人群。那么这部分人一定有一个需求就是，解决内耗。那么我针对这部分内耗人群，我应该怎么样去做<strong>流量型内容</strong>呢？</p>
<p>我可以在抖音去搜索「内耗」这个关键词：</p>
<p>a.结果会出来很多的高赞爆款视频。从这些爆款视频中我就可以知道：</p>
<p>1、「内耗人群」会被什么样的文案（内容/内容形式）吸引。</p>
<p>2、从这些爆款文案/评论区中我们可以知道用户的痛点</p>
<p>b.结果会出来一些非高赞爆款视频，一些很普通的视频也会出来。其中有些视频会有一个特点，就是它确实给出了解决方案，告诉你如何去解决内耗这个问题。</p>
<p>所以就衍生出来做 IP 的两种思路：</p>
<p>1）一种就是类似于「一个人的莎士比亚」，用户喜欢什么我就说什么。</p>
<p>2）实实在在给出解决方案的。比如我也是一个内耗的人，我是通过什么方式去解决内耗的。把这个方法分享给用户，真正帮你去解决你的内耗。</p>
<p>所以我们可以通过这种方式去理解你的用户，理解你用户有哪些痛点？理解你用户的痛点需要怎么去解决？去找同行进行学习。</p></div>
<div style="font-size:small;margin-top:8px;float:right;">转载请注明出处</div>

<button class="btn btn-block" type="button" onclick="openComments()" id="cmButton">评论</button>
<div class="comments" id="comments"></div>

</div>
    <div id="footer"><div id="footer1">Copyright © <span id="copyrightYear"></span> <a href="https://hepingfly.github.io">和平自留地</a></div>
<div id="footer2">
    <span id="runday"></span><span>Powered by <a href="https://meekdai.com/Gmeek.html" target="_blank">Gmeek</a></span>
</div>

<script>
var now=new Date();
document.getElementById("copyrightYear").innerHTML=now.getFullYear();

if("06/24/2024"!=""){
    var startSite=new Date("06/24/2024");
    var diff=now.getTime()-startSite.getTime();
    var diffDay=Math.floor(diff/(1000*60*60*24));
    document.getElementById("runday").innerHTML="网站运行"+diffDay+"天"+" • ";
}
</script></div>
</body>
<script>
var IconList={'sun': 'M8 10.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zM8 12a4 4 0 100-8 4 4 0 000 8zM8 0a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0V.75A.75.75 0 018 0zm0 13a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5A.75.75 0 018 13zM2.343 2.343a.75.75 0 011.061 0l1.06 1.061a.75.75 0 01-1.06 1.06l-1.06-1.06a.75.75 0 010-1.06zm9.193 9.193a.75.75 0 011.06 0l1.061 1.06a.75.75 0 01-1.06 1.061l-1.061-1.06a.75.75 0 010-1.061zM16 8a.75.75 0 01-.75.75h-1.5a.75.75 0 010-1.5h1.5A.75.75 0 0116 8zM3 8a.75.75 0 01-.75.75H.75a.75.75 0 010-1.5h1.5A.75.75 0 013 8zm10.657-5.657a.75.75 0 010 1.061l-1.061 1.06a.75.75 0 11-1.06-1.06l1.06-1.06a.75.75 0 011.06 0zm-9.193 9.193a.75.75 0 010 1.06l-1.06 1.061a.75.75 0 11-1.061-1.06l1.06-1.061a.75.75 0 011.061 0z', 'moon': 'M9.598 1.591a.75.75 0 01.785-.175 7 7 0 11-8.967 8.967.75.75 0 01.961-.96 5.5 5.5 0 007.046-*********** 0 01.175-.786zm1.616 1.945a7 7 0 01-7.678 7.678 5.5 5.5 0 107.678-7.678z', 'sync': 'M1.705 8.005a.75.75 0 0 1 .834.656 5.5 5.5 0 0 0 9.592 2.97l-1.204-1.204a.25.25 0 0 1 .177-.427h3.646a.25.25 0 0 1 .25.25v3.646a.25.25 0 0 1-.427.177l-1.38-1.38A7.002 7.002 0 0 1 1.05 8.84a.75.75 0 0 1 .656-.834ZM8 2.5a5.487 5.487 0 0 0-4.131 1.869l1.204 1.204A.25.25 0 0 1 4.896 6H1.25A.25.25 0 0 1 1 5.75V2.104a.25.25 0 0 1 .427-.177l1.38 1.38A7.002 7.002 0 0 1 14.95 7.16a.75.75 0 0 1-1.49.178A5.5 5.5 0 0 0 8 2.5Z', 'home': 'M6.906.664a1.749 1.749 0 0 1 2.187 0l5.25 4.2c.415.332.657.835.657 1.367v7.019A1.75 1.75 0 0 1 13.25 15h-3.5a.75.75 0 0 1-.75-.75V9H7v5.25a.75.75 0 0 1-.75.75h-3.5A1.75 1.75 0 0 1 1 13.25V6.23c0-.531.242-1.034.657-1.366l5.25-4.2Zm1.25 1.171a.25.25 0 0 0-.312 0l-5.25 4.2a.25.25 0 0 0-.094.196v7.019c0 .138.112.25.25.25H5.5V8.25a.75.75 0 0 1 .75-.75h3.5a.75.75 0 0 1 .75.75v5.25h2.75a.25.25 0 0 0 .25-.25V6.23a.25.25 0 0 0-.094-.195Z', 'github': 'M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z'};
var utterancesLoad=0;

let themeSettings={
    "dark": ["dark","moon","#00f0ff","dark-blue"],
    "light": ["light","sun","#ff5000","github-light"],
    "auto": ["auto","sync","","preferred-color-scheme"]
};
function changeTheme(mode, icon, color, utheme){
    document.documentElement.setAttribute("data-color-mode",mode);
    document.getElementById("themeSwitch").setAttribute("d",value=IconList[icon]);
    document.getElementById("themeSwitch").parentNode.style.color=color;
    if(utterancesLoad==1){utterancesTheme(utheme);}
}
function modeSwitch(){
    let currentMode=document.documentElement.getAttribute('data-color-mode');
    let newMode = currentMode === "light" ? "dark" : currentMode === "dark" ? "auto" : "light";
    localStorage.setItem("meek_theme", newMode);
    if(themeSettings[newMode]){
        changeTheme(...themeSettings[newMode]);
    }
}
function utterancesTheme(theme){
    const message={type:'set-theme',theme: theme};
    const iframe=document.getElementsByClassName('utterances-frame')[0];
    iframe.contentWindow.postMessage(message,'https://utteranc.es');
}
if(themeSettings[theme]){changeTheme(...themeSettings[theme]);}
console.log("\n %c Gmeek last https://github.com/Meekdai/Gmeek \n","padding:5px 0;background:#02d81d;color:#fff");
</script>

<script>
document.getElementById("pathHome").setAttribute("d",IconList["home"]);




function openComments(){
    cm=document.getElementById("comments");
    cmButton=document.getElementById("cmButton");
    cmButton.innerHTML="loading";
    span=document.createElement("span");
    span.setAttribute("class","AnimatedEllipsis");
    cmButton.appendChild(span);

    script=document.createElement("script");
    script.setAttribute("src","https://utteranc.es/client.js");
    script.setAttribute("repo","hepingfly/hepingfly.github.io");
    script.setAttribute("issue-term","title");
    
    if(localStorage.getItem("meek_theme")=="dark"){script.setAttribute("theme","dark-blue");}
    else if(localStorage.getItem("meek_theme")=="light") {script.setAttribute("theme","github-light");}
    else{script.setAttribute("theme","preferred-color-scheme");}
    
    script.setAttribute("crossorigin","anonymous");
    script.setAttribute("async","");
    cm.appendChild(script);

    int=self.setInterval("iFrameLoading()",200);
}

function iFrameLoading(){
    var utterances=document.getElementsByClassName('utterances');
    if(utterances.length==1){
        if(utterances[0].style.height!=""){
            utterancesLoad=1;
            int=window.clearInterval(int);
            document.getElementById("cmButton").style.display="none";
            console.log("utterances Load OK");
        }
    }
}



</script>
<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>

</html>
