/* 🎨 和平自留地 - 现代化UI样式 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');

/* 🌈 CSS变量定义 */
:root {
  /* 主色调 - 温暖的科技蓝 */
  --primary: #2563eb;
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  
  /* 辅助色 - 温暖的橙色系 */
  --accent: #f59e0b;
  --accent-light: #fbbf24;
  
  /* 中性色 - 高级灰 */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-300: #d4d4d4;
  --gray-400: #a3a3a3;
  --gray-500: #737373;
  --gray-600: #525252;
  --gray-700: #404040;
  --gray-800: #262626;
  --gray-900: #171717;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-card: #ffffff;
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* 间距 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
}

/* 🌙 暗色模式 */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-card: #334155;
  --gray-900: #f1f5f9;
  --gray-800: #e2e8f0;
  --gray-600: #94a3b8;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3);
}

/* 📝 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  font-size: 16px;
  line-height: 1.7;
  color: var(--gray-800);
  background: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 🏠 头部样式 */
.site-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  padding: var(--spacing-2xl) 0;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.site-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-lg);
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  object-fit: cover;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.site-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-sm);
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.site-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
}

/* 📄 主容器 */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-lg);
}

/* 📝 文章卡片 */
.post-list {
  display: grid;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.post-card {
  background: var(--bg-card);
  border-radius: 16px;
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.post-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--accent));
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: left;
}

.post-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.post-card:hover::before {
  transform: scaleX(1);
}

.post-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md);
  line-height: 1.3;
}

.post-title a {
  color: var(--gray-900);
  text-decoration: none;
  transition: color 0.3s ease;
}

.post-title a:hover {
  color: var(--primary);
}

.post-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.post-meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.post-excerpt {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.read-more {
  display: inline-flex;
  align-items: center;
  color: var(--primary);
  font-weight: 500;
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.read-more:hover {
  transform: translateX(4px);
}

.read-more::after {
  content: '→';
  margin-left: 0.25rem;
  transition: transform 0.3s ease;
}

.read-more:hover::after {
  transform: translateX(2px);
}

/* 🏷️ 标签样式 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-md);
}

.tag {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: var(--gray-100);
  color: var(--gray-700);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.tag:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-1px);
}

/* 📱 响应式设计 */
@media (max-width: 768px) {
  .site-header {
    padding: var(--spacing-xl) 0;
  }
  
  .site-title {
    font-size: 2rem;
  }
  
  .site-subtitle {
    font-size: 1rem;
  }
  
  .avatar {
    width: 80px;
    height: 80px;
  }
  
  .main-container {
    padding: var(--spacing-xl) var(--spacing-md);
  }
  
  .post-card {
    padding: var(--spacing-lg);
    margin: 0 -var(--spacing-md);
    border-radius: 0;
  }
  
  .post-title {
    font-size: 1.25rem;
  }
  
  .post-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .main-container {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
}

/* ✨ 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.post-card {
  animation: fadeInUp 0.6s ease forwards;
}

.post-card:nth-child(1) { animation-delay: 0.1s; }
.post-card:nth-child(2) { animation-delay: 0.2s; }
.post-card:nth-child(3) { animation-delay: 0.3s; }
.post-card:nth-child(4) { animation-delay: 0.4s; }

/* 🌙 暗色模式切换按钮 */
.theme-toggle {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--bg-card);
  border: 1px solid var(--gray-200);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  z-index: 1000;
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* 📊 统计数字样式 */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  margin: var(--spacing-lg) 0;
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  display: block;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* 🔍 搜索系统 */
.search-container {
  margin-bottom: var(--spacing-xl);
}

.search-box {
  position: relative;
  max-width: 600px;
  margin: 0 auto var(--spacing-md);
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  padding-left: 3rem;
  border: 1px solid var(--gray-300);
  border-radius: 9999px;
  font-size: 1rem;
  background: var(--bg-card);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), var(--shadow-md);
}

.search-input::placeholder {
  color: var(--gray-500);
}

.search-btn {
  position: absolute;
  right: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-50%) scale(1.05);
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.filter-label {
  color: var(--gray-600);
  font-size: 0.875rem;
  margin-right: var(--spacing-sm);
}

.filter-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--gray-100);
  color: var(--gray-700);
  border: none;
  border-radius: 9999px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tag:hover {
  background: var(--gray-200);
  transform: translateY(-1px);
}

.filter-tag.active {
  background: var(--primary);
  color: white;
}

.search-results {
  text-align: center;
  margin: var(--spacing-md) 0;
}

.results-info {
  color: var(--gray-600);
  font-size: 0.875rem;
}

.no-results {
  color: var(--gray-500);
  text-align: center;
  padding: var(--spacing-xl);
}

/* 📄 分页系统 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  margin: var(--spacing-2xl) 0;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--gray-300);
  background: var(--bg-card);
  color: var(--gray-700);
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
}

.pagination-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-1px);
}

.pagination-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-stats {
  text-align: center;
  color: var(--gray-600);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-md);
}

/* 📱 移动端搜索优化 */
@media (max-width: 768px) {
  .search-box {
    margin: 0 var(--spacing-md) var(--spacing-md);
  }
  
  .filter-tags {
    justify-content: flex-start;
    overflow-x: auto;
    padding: 0 var(--spacing-md);
  }
  
  .pagination-container {
    gap: var(--spacing-xs);
  }
  
  .pagination-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    min-width: 36px;
  }
}

/* 🔍 搜索框样式 */
.search-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto var(--spacing-xl);
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-left: 2.5rem;
  border: 1px solid var(--gray-300);
  border-radius: 9999px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--bg-card);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500);
}

/* 📝 文章详情页样式 */
.post-content {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  background: var(--bg-card);
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
}

.post-content h1,
.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  margin: var(--spacing-lg) 0 var(--spacing-md);
  color: var(--gray-900);
  line-height: 1.3;
}

.post-content p {
  margin-bottom: var(--spacing-md);
  color: var(--gray-700);
}

.post-content code {
  background: var(--gray-100);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.875em;
  color: var(--primary);
}

.post-content pre {
  background: var(--gray-900);
  color: var(--gray-100);
  padding: var(--spacing-md);
  border-radius: 8px;
  overflow-x: auto;
  margin: var(--spacing-md) 0;
}

/* 🔄 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 📱 移动端菜单 */
.mobile-menu {
  display: none;
}

@media (max-width: 768px) {
  .mobile-menu {
    display: block;
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    z-index: 1000;
  }
}