// 🔍 智能搜索系统
class BlogSearch {
  constructor() {
    this.allPosts = [];
    this.searchResults = [];
    this.init();
  }

  init() {
    this.loadPosts();
    this.createSearchUI();
    this.bindEvents();
  }

  loadPosts() {
    const posts = document.querySelectorAll('.post-card');
    this.allPosts = Array.from(posts).map(post => ({
      element: post,
      title: post.querySelector('.post-title').textContent.trim(),
      excerpt: post.querySelector('.post-excerpt').textContent.trim(),
      date: post.querySelector('.post-meta-item').textContent.trim(),
      tags: Array.from(post.querySelectorAll('.post-meta-item')).slice(1)
                 .map(tag => tag.textContent.trim().replace('🏷️ ', ''))
    }));
  }

  createSearchUI() {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
      <div class="search-box">
        <input type="text" class="search-input" placeholder="搜索文章标题、内容或标签..." />
        <button class="search-btn">🔍</button>
      </div>
      <div class="filter-tags">
        <span class="filter-label">快速筛选：</span>
        <button class="filter-tag" data-tag="个人IP">个人IP</button>
        <button class="filter-tag" data-tag="随笔">随笔</button>
        <button class="filter-tag" data-tag="读书">读书</button>
      </div>
      <div class="search-results"></div>
    `;
    
    document.querySelector('.main-container').insertBefore(
      searchContainer, 
      document.querySelector('.post-list')
    );
  }

  search(query) {
    if (!query.trim()) {
      this.showAllPosts();
      return;
    }

    const searchTerm = query.toLowerCase();
    this.searchResults = this.allPosts.filter(post => 
      post.title.toLowerCase().includes(searchTerm) ||
      post.excerpt.toLowerCase().includes(searchTerm) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );

    this.renderResults();
  }

  filterByTag(tag) {
    this.searchResults = this.allPosts.filter(post => 
      post.tags.includes(tag)
    );
    this.renderResults();
  }

  renderResults() {
    const resultsContainer = document.querySelector('.search-results');
    const postList = document.querySelector('.post-list');
    
    if (this.searchResults.length === 0) {
      resultsContainer.innerHTML = `
        <div class="no-results">
          <p>😅 没有找到相关文章</p>
          <p>试试其他关键词？</p>
        </div>
      `;
    } else {
      resultsContainer.innerHTML = `
        <div class="results-info">
          找到 ${this.searchResults.length} 篇文章
        </div>
      `;
    }

    // 隐藏/显示文章
    this.allPosts.forEach(post => {
      const isVisible = this.searchResults.includes(post);
      post.element.style.display = isVisible ? 'block' : 'none';
    });
  }

  showAllPosts() {
    this.allPosts.forEach(post => {
      post.element.style.display = 'block';
    });
    document.querySelector('.search-results').innerHTML = '';
  }

  bindEvents() {
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    const filterTags = document.querySelectorAll('.filter-tag');

    searchInput.addEventListener('input', (e) => {
      this.search(e.target.value);
    });

    searchBtn.addEventListener('click', () => {
      this.search(searchInput.value);
    });

    filterTags.forEach(tag => {
      tag.addEventListener('click', (e) => {
        e.target.classList.toggle('active');
        const tagName = e.target.dataset.tag;
        
        if (e.target.classList.contains('active')) {
          this.filterByTag(tagName);
        } else {
          this.showAllPosts();
        }
      });
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        searchInput.focus();
      }
    });
  }
}

// 🎯 初始化搜索系统
document.addEventListener('DOMContentLoaded', () => {
  new BlogSearch();
});