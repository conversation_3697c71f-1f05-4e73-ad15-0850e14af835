<!DOCTYPE html>
<html data-color-mode="light" data-dark-theme="dark" data-light-theme="light" lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="content-type" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href='https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/Primer/21.0.7/primer.css' rel='stylesheet' />
    <script src='https://hepingfly.github.io/GmeekVercount.js'></script>
    <link rel="icon" href="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG"><script>
        let theme = localStorage.getItem("meek_theme") || "light";
        document.documentElement.setAttribute("data-color-mode", theme);
    </script>
<meta name="description" content="如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹">
<meta property="og:title" content="和平自留地">
<meta property="og:description" content="如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹">
<meta property="og:type" content="blog">
<meta property="og:url" content="https://hepingfly.github.io">
<meta property="og:image" content="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG">
<title>和平自留地</title>

</head>
<style>
body{box-sizing: border-box;min-width: 200px;max-width: 900px;margin: 20px auto;padding: 45px;font-size: 16px;font-family: sans-serif;line-height: 1.25;}
#header{display:flex;padding-bottom:8px;border-bottom: 1px solid var(--borderColor-muted, var(--color-border-muted));margin-bottom: 16px;}
#footer {margin-top:64px; text-align: center;font-size: small;}

</style>

<style>
.avatar {transition: 0.8s;width:64px;height:64px;object-fit: cover;}
.avatar:hover{transform: scale(1.15) rotate(360deg);}
#header h1 a{color:inherit;text-decoration:none;vertical-align: bottom;font-size:40px;font-family:Monaco;margin-left:8px;}
.title-right{display:flex;margin:auto 0 0 auto;}
.title-right button{margin-right:8px;padding:16px;}
.title-right .circle{padding: 14px 16px;}

.SideNav{min-width: 360px;}
.SideNav-icon{margin-right: 16px}
.SideNav-item .Label{color: #fff;margin-left:4px;}
.d-flex{min-width:0;}
.listTitle{overflow:hidden;white-space:nowrap;text-overflow: ellipsis;max-width: 100%;}
.listLabels{white-space:nowrap;display:flex;}
.listLabels object{max-height:16px;max-width:24px;}

@media (max-width: 600px) {
    body {padding: 8px;}
    .avatar {width:40px;height:40px;}
    .blogTitle{display:none;}
    #buttonRSS{display:none;}
    .LabelTime{display:none;}
}
</style>



<body>
    <div id="header">
<h1>
    <img src="https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG" class="avatar circle" id="avatarImg" alt="avatar"><a class="blogTitle">和平自留地</a></h1>
<div class="title-right">
    <a href="https://hepingfly.github.io/tag.html" id="buttonSearch" class="btn btn-invisible circle" title="搜索">
        <svg class="octicon" width="16" height="16" >
            <path id="pathSearch" fill-rule="evenodd"></path>
        </svg>
    </a>
    <a href="https://hepingfly.github.io/reward.html" class="btn btn-invisible circle" title="reward" target="_blank">
        <svg class="octicon" width="16" height="16" >
            <path id="reward" fill-rule="evenodd"></path>
        </svg>
    </a>
    
    <a href="https://hepingfly.github.io/rss.xml" target="_blank" id="buttonRSS" class="btn btn-invisible circle" title="RSS">
        <svg class="octicon" width="16" height="16" >
            <path id="pathRSS" fill-rule="evenodd"></path>
        </svg>
    </a>
    <a class="btn btn-invisible circle" onclick="modeSwitch()" title="切换主题">
        <svg class="octicon" width="16" height="16" >
            <path id="themeSwitch" fill-rule="evenodd"></path>
        </svg>
    </a>
</div>
</div>
    <div id="content">
<div style="margin-bottom: 16px;">如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹</div>
<nav class="SideNav border">
<a class="SideNav-item d-flex flex-items-center flex-justify-between" href="post/su-ren-du-shu-IP-ying-gai-zen-me-zuo-%EF%BC%9F.html">
    <div class="d-flex flex-items-center">
        <svg class="SideNav-icon octicon" style="witdh:16px;height:16px"><path class="svgTop0" d=""></path>
        </svg>
        <span class="listTitle">素人读书IP应该怎么做？</span>
    </div>
    <div class="listLabels">
        
        <span class="Label LabelName" style="background-color:#69A6B0"><object><a style="color:#fff" href="tag.html#个人IP">个人IP</a></object></span>
        <span class="Label LabelTime" style="background-color:#bc4c00">2024-08-04</span>
    </div>
</a><a class="SideNav-item d-flex flex-items-center flex-justify-between" href="post/yan-shen-xun-lian.html">
    <div class="d-flex flex-items-center">
        <svg class="SideNav-icon octicon" style="witdh:16px;height:16px"><path class="svgTop0" d=""></path>
        </svg>
        <span class="listTitle">眼神训练</span>
    </div>
    <div class="listLabels">
        
        <span class="Label LabelName" style="background-color:#69A6B0"><object><a style="color:#fff" href="tag.html#个人IP">个人IP</a></object></span><span class="Label LabelName" style="background-color:#35A1CD"><object><a style="color:#fff" href="tag.html#随笔">随笔</a></object></span>
        <span class="Label LabelTime" style="background-color:#bc4c00">2024-07-22</span>
    </div>
</a><a class="SideNav-item d-flex flex-items-center flex-justify-between" href="post/mei-ge-ren-du-ke-yi-you-zi-ji-de-ge-ren-pin-pai.html">
    <div class="d-flex flex-items-center">
        <svg class="SideNav-icon octicon" style="witdh:16px;height:16px"><path class="svgTop0" d=""></path>
        </svg>
        <span class="listTitle">每个人都可以有自己的个人品牌</span>
    </div>
    <div class="listLabels">
        
        <span class="Label LabelName" style="background-color:#69A6B0"><object><a style="color:#fff" href="tag.html#个人IP">个人IP</a></object></span>
        <span class="Label LabelTime" style="background-color:#bc4c00">2024-07-16</span>
    </div>
</a><a class="SideNav-item d-flex flex-items-center flex-justify-between" href="post/ge-ren-IP-di-ceng-luo-ji.html">
    <div class="d-flex flex-items-center">
        <svg class="SideNav-icon octicon" style="witdh:16px;height:16px"><path class="svgTop0" d=""></path>
        </svg>
        <span class="listTitle">个人IP底层逻辑</span>
    </div>
    <div class="listLabels">
        
        <span class="Label LabelName" style="background-color:#69A6B0"><object><a style="color:#fff" href="tag.html#个人IP">个人IP</a></object></span>
        <span class="Label LabelTime" style="background-color:#bc4c00">2024-07-13</span>
    </div>
</a>
</nav>
</div>
    <div id="footer"><div id="footer1">Copyright © <span id="copyrightYear"></span> <a href="https://hepingfly.github.io">和平自留地</a></div>
<div id="footer2">
    <span id="runday"></span><span>Powered by <a href="https://meekdai.com/Gmeek.html" target="_blank">Gmeek</a></span>
</div>

<script>
var now=new Date();
document.getElementById("copyrightYear").innerHTML=now.getFullYear();

if("06/24/2024"!=""){
    var startSite=new Date("06/24/2024");
    var diff=now.getTime()-startSite.getTime();
    var diffDay=Math.floor(diff/(1000*60*60*24));
    document.getElementById("runday").innerHTML="网站运行"+diffDay+"天"+" • ";
}
</script></div>
</body>
<script>
var IconList={'sun': 'M8 10.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zM8 12a4 4 0 100-8 4 4 0 000 8zM8 0a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0V.75A.75.75 0 018 0zm0 13a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5A.75.75 0 018 13zM2.343 2.343a.75.75 0 011.061 0l1.06 1.061a.75.75 0 01-1.06 1.06l-1.06-1.06a.75.75 0 010-1.06zm9.193 9.193a.75.75 0 011.06 0l1.061 1.06a.75.75 0 01-1.06 1.061l-1.061-1.06a.75.75 0 010-1.061zM16 8a.75.75 0 01-.75.75h-1.5a.75.75 0 010-1.5h1.5A.75.75 0 0116 8zM3 8a.75.75 0 01-.75.75H.75a.75.75 0 010-1.5h1.5A.75.75 0 013 8zm10.657-5.657a.75.75 0 010 1.061l-1.061 1.06a.75.75 0 11-1.06-1.06l1.06-1.06a.75.75 0 011.06 0zm-9.193 9.193a.75.75 0 010 1.06l-1.06 1.061a.75.75 0 11-1.061-1.06l1.06-1.061a.75.75 0 011.061 0z', 'moon': 'M9.598 1.591a.75.75 0 01.785-.175 7 7 0 11-8.967 8.967.75.75 0 01.961-.96 5.5 5.5 0 007.046-*********** 0 01.175-.786zm1.616 1.945a7 7 0 01-7.678 7.678 5.5 5.5 0 107.678-7.678z', 'sync': 'M1.705 8.005a.75.75 0 0 1 .834.656 5.5 5.5 0 0 0 9.592 2.97l-1.204-1.204a.25.25 0 0 1 .177-.427h3.646a.25.25 0 0 1 .25.25v3.646a.25.25 0 0 1-.427.177l-1.38-1.38A7.002 7.002 0 0 1 1.05 8.84a.75.75 0 0 1 .656-.834ZM8 2.5a5.487 5.487 0 0 0-4.131 1.869l1.204 1.204A.25.25 0 0 1 4.896 6H1.25A.25.25 0 0 1 1 5.75V2.104a.25.25 0 0 1 .427-.177l1.38 1.38A7.002 7.002 0 0 1 14.95 7.16a.75.75 0 0 1-1.49.178A5.5 5.5 0 0 0 8 2.5Z', 'search': 'M15.7 13.3l-3.81-3.83A5.93 5.93 0 0 0 13 6c0-3.31-2.69-6-6-6S1 2.69 1 6s2.69 6 6 6c1.3 0 2.48-.41 3.47-1.11l3.83 3.81c.19.2.45.3.7.3.25 0 .52-.09.7-.3a.996.996 0 0 0 0-1.41v.01zM7 10.7c-2.59 0-4.7-2.11-4.7-4.7 0-2.59 2.11-4.7 4.7-4.7 2.59 0 4.7 2.11 4.7 4.7 0 2.59-2.11 4.7-4.7 4.7z', 'rss': 'M2.002 2.725a.75.75 0 0 1 .797-.699C8.79 2.42 13.58 7.21 13.974 13.201a.75.75 0 0 1-1.497.098 10.502 10.502 0 0 0-9.776-9.776.747.747 0 0 1-.7-.798ZM2.84 7.05h-.002a7.002 7.002 0 0 1 6.113 6.111.75.75 0 0 1-1.49.178 5.503 5.503 0 0 0-4.8-4.8.75.75 0 0 1 .179-1.489ZM2 13a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z', 'upload': 'M2.75 14A1.75 1.75 0 0 1 1 12.25v-2.5a.75.75 0 0 1 1.5 0v2.5c0 .138.112.25.25.25h10.5a.25.25 0 0 0 .25-.25v-2.5a.75.75 0 0 1 1.5 0v2.5A1.75 1.75 0 0 1 13.25 14Z M11.78 4.72a.749.749 0 1 1-1.06 1.06L8.75 3.811V9.5a.75.75 0 0 1-1.5 0V3.811L5.28 5.78a.749.749 0 1 1-1.06-1.06l3.25-3.25a.749.749 0 0 1 1.06 0l3.25 3.25Z', 'post': 'M0 3.75C0 2.784.784 2 1.75 2h12.5c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 14.25 14H1.75A1.75 1.75 0 0 1 0 12.25Zm1.75-.25a.25.25 0 0 0-.25.25v8.5c0 .138.112.25.25.25h12.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25ZM3.5 6.25a.75.75 0 0 1 .75-.75h7a.75.75 0 0 1 0 1.5h-7a.75.75 0 0 1-.75-.75Zm.75 2.25h4a.75.75 0 0 1 0 1.5h-4a.75.75 0 0 1 0-1.5Z', 'reward': 'M8.834.066c.763.087 1.5.295 2.01.884.505.581.656 1.378.656 2.3 0 .467-.087 1.119-.157 1.637L11.328 5h1.422c.603 0 1.174.085 1.668.333.508.254.911.679 1.137 1.2.453.998.438 2.447.188 4.316l-.04.306c-.105.79-.195 1.473-.313 2.033-.131.63-.315 1.209-.668 1.672C13.97 15.847 12.706 16 11 16c-1.848 0-3.234-.333-4.388-.653-.165-.045-.323-.09-.475-.133-.658-.186-1.2-.34-1.725-.415A1.75 1.75 0 0 1 2.75 16h-1A1.75 1.75 0 0 1 0 14.25v-7.5C0 5.784.784 5 1.75 5h1a1.75 1.75 0 0 1 1.514.872c.258-.105.59-.268.918-.508C5.853 4.874 6.5 4.079 6.5 2.75v-.5c0-1.202.994-2.337 2.334-2.184ZM4.5 13.3c.705.088 1.39.284 2.072.478l.441.125c1.096.305 2.334.598 3.987.598 1.794 0 2.28-.223 2.528-.549.147-.193.276-.505.394-1.07.105-.502.188-1.124.295-1.93l.04-.3c.25-1.882.189-2.933-.068-3.497a.921.921 0 0 0-.442-.48c-.208-.104-.52-.174-.997-.174H11c-.686 0-1.295-.577-1.206-1.336.023-.192.05-.39.076-.586.065-.488.13-.97.13-1.328 0-.809-.144-1.15-.288-1.316-.137-.158-.402-.304-1.048-.378C8.357 1.521 8 1.793 8 2.25v.5c0 1.922-.978 3.128-1.933 3.825a5.831 5.831 0 0 1-1.567.81ZM2.75 6.5h-1a.25.25 0 0 0-.25.25v7.5c0 .138.112.25.25.25h1a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z'};
var utterancesLoad=0;

let themeSettings={
    "dark": ["dark","moon","#00f0ff","dark-blue"],
    "light": ["light","sun","#ff5000","github-light"],
    "auto": ["auto","sync","","preferred-color-scheme"]
};
function changeTheme(mode, icon, color, utheme){
    document.documentElement.setAttribute("data-color-mode",mode);
    document.getElementById("themeSwitch").setAttribute("d",value=IconList[icon]);
    document.getElementById("themeSwitch").parentNode.style.color=color;
    if(utterancesLoad==1){utterancesTheme(utheme);}
}
function modeSwitch(){
    let currentMode=document.documentElement.getAttribute('data-color-mode');
    let newMode = currentMode === "light" ? "dark" : currentMode === "dark" ? "auto" : "light";
    localStorage.setItem("meek_theme", newMode);
    if(themeSettings[newMode]){
        changeTheme(...themeSettings[newMode]);
    }
}
function utterancesTheme(theme){
    const message={type:'set-theme',theme: theme};
    const iframe=document.getElementsByClassName('utterances-frame')[0];
    iframe.contentWindow.postMessage(message,'https://utteranc.es');
}
if(themeSettings[theme]){changeTheme(...themeSettings[theme]);}
console.log("\n %c Gmeek last https://github.com/Meekdai/Gmeek \n","padding:5px 0;background:#02d81d;color:#fff");
</script>

<script>
document.getElementById("pathSearch").setAttribute("d",IconList["search"]);
document.getElementById("pathRSS").setAttribute("d",IconList["rss"]);
iconTOP=document.getElementsByClassName("svgTop1");
iconPost=document.getElementsByClassName("svgTop0");
for(var i=0;i<iconTOP.length;i++){
    iconTOP[i].setAttribute("d",IconList["upload"]);
    iconTOP[i].parentNode.style.color="red";
}
for(var i=0;i<iconPost.length;i++){
    iconPost[i].setAttribute("d",IconList["post"]);
}


document.getElementById("reward").setAttribute("d",value=IconList["reward"]);


</script>


</html>
