document.addEventListener("DOMContentLoaded", function() {
    // 创建打赏容器
    const donationWrapperHtml = `
<div class="donation-wrapper">
    <div class="donation-content">
        <h3 class="donation-title">感谢您的支持！</h3>
        <p class="donation-subtitle">如果这篇文章对您有帮助，请随意打赏</p>
        <button id="donation-button" class="donation-button">赞赏支持</button>
    </div>
</div>
`;

    // 创建模态框
    const modalHtml = `
<div id="donation-modal" class="donation-modal">
    <div class="donation-modal-content">
        <button class="modal-close" id="modal-close">&times;</button>
        <h2 class="modal-title">感谢您的支持！</h2>
        <p class="modal-description">扫码打赏，支持原创</p>
        <div class="qr-container">
            <div class="qr-item">
                <div class="qr-label">
                    <span>💚</span>
                    <span>微信打赏</span>
                </div>
                <img src="https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E5%BE%AE%E4%BF%A1%E8%B5%9E%E8%B5%8F%E7%A0%81.JPG" alt="微信支付二维码" class="qr-code">
                <p class="qr-note">微信扫一扫，支持原创</p>
            </div>
            <div class="qr-item">
                <div class="qr-label">
                    <span>💙</span>
                    <span>支付宝打赏</span>
                </div>
                <img src="https://cdn.jsdelivr.net/gh/hepingfly/images@main/%E6%94%AF%E4%BB%98%E5%AE%9D%E6%94%B6%E6%AC%BE%E7%A0%81.jpg" alt="支付宝二维码" class="qr-code">
                <p class="qr-note">支付宝扫一扫，支持原创</p>
            </div>
        </div>
    </div>
</div>
`;

    // CSS样式
    const style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = `
        /* 打赏容器样式 */
        .donation-wrapper {
            margin: 3rem 0;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .donation-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,6 C10,6 4,0 0,6 C-4,12 4,18 10,24 C16,18 24,12 20,6 C16,0 10,6 10,6 Z" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
            opacity: 0.3;
        }

        .donation-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .donation-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .donation-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            margin-bottom: 2rem;
        }

        .donation-button {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            padding: 1rem 2.5rem;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .donation-button::before {
            content: '💖';
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .donation-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
        }

        .donation-button:active {
            transform: translateY(0);
        }

        .donation-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .donation-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .donation-modal-content {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.1);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(0, 0, 0, 0.2);
            transform: rotate(90deg);
        }

        .qr-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .qr-item {
            text-align: center;
            padding: 1.5rem;
            background: var(--gray-50);
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .qr-item:hover {
            transform: translateY(-5px);
        }

        .qr-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .qr-code:hover {
            transform: scale(1.05);
        }

        .qr-note {
            margin-top: 1rem;
            font-size: 0.9rem;
            color: var(--gray-600);
            line-height: 1.4;
        }

        .modal-title {
            text-align: center;
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .modal-description {
            text-align: center;
            color: var(--gray-600);
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(50px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .donation-wrapper {
                margin: 2rem 1rem;
                padding: 1.5rem;
            }

            .qr-container {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .qr-code {
                width: 150px;
                height: 150px;
            }

            .donation-modal-content {
                margin: 1rem;
                padding: 1.5rem;
            }

            .donation-button {
                padding: 0.8rem 2rem;
                font-size: 1rem;
            }
        }

        [data-theme="dark"] .donation-modal-content {
            background: var(--bg-card);
            color: var(--gray-100);
        }

        [data-theme="dark"] .qr-item {
            background: var(--gray-800);
        }

        [data-theme="dark"] .qr-label {
            color: var(--gray-100);
        }

        [data-theme="dark"] .modal-title {
            color: var(--gray-100);
        }

        [data-theme="dark"] .modal-description {
            color: var(--gray-300);
        }
    `;
    document.head.appendChild(style);

    // 获取页面上你希望按钮插入的div元素
    const targetDiv = document.querySelector('#postBody'); // 确保页面中有id为targetDiv的元素

    // 将打赏容器插入到文章底部
    targetDiv.insertAdjacentHTML('beforeend', donationWrapperHtml);
    
    // 将模态框插入到body末尾
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 获取元素
    const donationButton = document.getElementById("donation-button");
    const donationModal = document.getElementById("donation-modal");
    const modalClose = document.getElementById("modal-close");

    // 打开模态框
    donationButton.addEventListener("click", function() {
        donationModal.classList.add('active');
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    });

    // 关闭模态框
    modalClose.addEventListener("click", function() {
        donationModal.classList.remove('active');
        document.body.style.overflow = ''; // 恢复滚动
    });

    // 点击模态框外部关闭
    donationModal.addEventListener("click", function(e) {
        if (e.target === donationModal) {
            donationModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    });

    // ESC键关闭模态框
    document.addEventListener("keydown", function(e) {
        if (e.key === 'Escape' && donationModal.classList.contains('active')) {
            donationModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    });
});
