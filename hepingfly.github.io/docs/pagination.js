// 📄 智能分页系统
class BlogPagination {
  constructor(itemsPerPage = 10) {
    this.itemsPerPage = itemsPerPage;
    this.currentPage = 1;
    this.allPosts = [];
    this.filteredPosts = [];
    
    this.init();
  }

  init() {
    this.loadPosts();
    this.renderPagination();
    this.bindEvents();
  }

  loadPosts() {
    // 从现有的DOM中提取所有文章
    const posts = document.querySelectorAll('.post-card');
    this.allPosts = Array.from(posts).map(post => ({
      element: post,
      title: post.querySelector('.post-title').textContent,
      date: post.querySelector('.post-meta-item').textContent,
      tags: Array.from(post.querySelectorAll('.post-meta-item')).slice(1)
    }));
    this.filteredPosts = [...this.allPosts];
  }

  renderPage(page) {
    this.currentPage = page;
    const startIndex = (page - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    
    // 隐藏所有文章
    this.allPosts.forEach(post => {
      post.element.style.display = 'none';
    });
    
    // 显示当前页的文章
    this.filteredPosts.slice(startIndex, endIndex).forEach(post => {
      post.element.style.display = 'block';
    });
    
    this.renderPagination();
    this.scrollToTop();
  }

  renderPagination() {
    const totalPages = Math.ceil(this.filteredPosts.length / this.itemsPerPage);
    const paginationContainer = this.getOrCreatePaginationContainer();
    
    paginationContainer.innerHTML = '';
    
    // 上一页按钮
    if (this.currentPage > 1) {
      const prevBtn = this.createPaginationBtn('上一页', this.currentPage - 1);
      paginationContainer.appendChild(prevBtn);
    }
    
    // 页码按钮
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      const pageBtn = this.createPaginationBtn(i, i);
      if (i === this.currentPage) {
        pageBtn.classList.add('active');
      }
      paginationContainer.appendChild(pageBtn);
    }
    
    // 下一页按钮
    if (this.currentPage < totalPages) {
      const nextBtn = this.createPaginationBtn('下一页', this.currentPage + 1);
      paginationContainer.appendChild(nextBtn);
    }
    
    // 显示统计信息
    this.renderStats();
  }

  createPaginationBtn(text, page) {
    const btn = document.createElement('button');
    btn.textContent = text;
    btn.className = 'pagination-btn';
    btn.addEventListener('click', () => this.renderPage(page));
    return btn;
  }

  getOrCreatePaginationContainer() {
    let container = document.querySelector('.pagination-container');
    if (!container) {
      container = document.createElement('div');
      container.className = 'pagination-container';
      document.querySelector('.main-container').appendChild(container);
    }
    return container;
  }

  renderStats() {
    const totalPosts = this.filteredPosts.length;
    const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endItem = Math.min(this.currentPage * this.itemsPerPage, totalPosts);
    
    const statsContainer = this.getOrCreateStatsContainer();
    statsContainer.innerHTML = `
      <div class="pagination-stats">
        显示 ${startItem}-${endItem} 条，共 ${totalPosts} 条文章
      </div>
    `;
  }

  getOrCreateStatsContainer() {
    let container = document.querySelector('.pagination-stats-container');
    if (!container) {
      container = document.createElement('div');
      container.className = 'pagination-stats-container';
      this.getOrCreatePaginationContainer().insertBefore(
        container, 
        this.getOrCreatePaginationContainer().firstChild
      );
    }
    return container;
  }

  searchPosts(query) {
    this.filteredPosts = this.allPosts.filter(post => 
      post.title.toLowerCase().includes(query.toLowerCase()) ||
      post.tags.some(tag => tag.textContent.toLowerCase().includes(query.toLowerCase()))
    );
    this.currentPage = 1;
    this.renderPage(1);
  }

  filterByTag(tag) {
    this.filteredPosts = this.allPosts.filter(post => 
      post.tags.some(t => t.textContent.includes(tag))
    );
    this.currentPage = 1;
    this.renderPage(1);
  }

  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  bindEvents() {
    // 搜索功能
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.searchPosts(e.target.value);
      });
    }
  }
}

// 🚀 初始化分页系统
document.addEventListener('DOMContentLoaded', () => {
  new BlogPagination(8); // 每页显示8篇文章
});