{"singlePage": ["reward"], "startSite": "06/24/2024", "filingNum": "", "onePageListNum": 15, "commentLabelColor": "#006b75", "yearColorList": ["#bc4c00", "#0969da", "#1f883d", "#A333D0"], "i18n": "CN", "themeMode": "manual", "dayTheme": "light", "nightTheme": "dark", "urlMode": "pinyin", "script": "<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>", "style": "", "head": "", "indexScript": "", "indexStyle": "", "bottomText": "转载请注明出处", "showPostSource": 0, "iconList": {"reward": "M8.834.066c.763.087 1.5.295 2.01.884.505.581.656 1.378.656 2.3 0 .467-.087 1.119-.157 1.637L11.328 5h1.422c.603 0 1.174.085 1.668.333.508.254.911.679 1.137 1.2.453.998.438 2.447.188 4.316l-.04.306c-.105.79-.195 1.473-.313 2.033-.131.63-.315 1.209-.668 1.672C13.97 15.847 12.706 16 11 16c-1.848 0-3.234-.333-4.388-.653-.165-.045-.323-.09-.475-.133-.658-.186-1.2-.34-1.725-.415A1.75 1.75 0 0 1 2.75 16h-1A1.75 1.75 0 0 1 0 14.25v-7.5C0 5.784.784 5 1.75 5h1a1.75 1.75 0 0 1 1.514.872c.258-.105.59-.268.918-.508C5.853 4.874 6.5 4.079 6.5 2.75v-.5c0-1.202.994-2.337 2.334-2.184ZM4.5 13.3c.705.088 1.39.284 2.072.478l.441.125c1.096.305 2.334.598 3.987.598 1.794 0 2.28-.223 2.528-.549.147-.193.276-.505.394-1.07.105-.502.188-1.124.295-1.93l.04-.3c.25-1.882.189-2.933-.068-3.497a.921.921 0 0 0-.442-.48c-.208-.104-.52-.174-.997-.174H11c-.686 0-1.295-.577-1.206-1.336.023-.192.05-.39.076-.586.065-.488.13-.97.13-1.328 0-.809-.144-1.15-.288-1.316-.137-.158-.402-.304-1.048-.378C8.357 1.521 8 1.793 8 2.25v.5c0 1.922-.978 3.128-1.933 3.825a5.831 5.831 0 0 1-1.567.81ZM2.75 6.5h-1a.25.25 0 0 0-.25.25v7.5c0 .*************.25h1a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"}, "UTC": 8, "rssSplit": "sentence", "exlink": {"reward": "https://hepingfly.github.io/reward.html"}, "needComment": 1, "allHead": "<script src='https://hepingfly.github.io/GmeekVercount.js'></script>", "title": "和平自留地", "subTitle": "如果互联网崩塌，希望这里能留下一点我曾经来过的痕迹", "avatarUrl": "https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG", "GMEEK_VERSION": "last", "postListJson": {"P2": {"htmlDir": "docs/post/ge-ren-<PERSON>-di-ceng-luo-ji.html", "labels": ["个人IP"], "postTitle": "个人IP底层逻辑", "postUrl": "post/ge-ren-<PERSON>-di-ceng-luo-ji.html", "postSourceUrl": "https://github.com/hepingfly/hepingfly.github.io/issues/2", "commentNum": 0, "wordCount": 2384, "description": "想要做成一个 IP，除了要有真人出镜的表现力之外，你还需要懂人群，懂需求，你是能够真正帮助这部分人群去解决他们的问题的。", "top": 0, "createdAt": 1720803740, "style": "", "script": "<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>", "head": "", "ogImage": "https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG", "createdDate": "2024-07-13", "dateLabelColor": "#bc4c00"}, "P3": {"htmlDir": "docs/post/mei-ge-ren-du-ke-yi-you-zi-ji-de-ge-ren-pin-pai.html", "labels": ["个人IP"], "postTitle": "每个人都可以有自己的个人品牌", "postUrl": "post/mei-ge-ren-du-ke-yi-you-zi-ji-de-ge-ren-pin-pai.html", "postSourceUrl": "https://github.com/hepingfly/hepingfly.github.io/issues/3", "commentNum": 0, "wordCount": 2583, "description": "## 1、前言\r\n\r\n**个人品牌有什么用？**\r\n\r\n1、帮你快速获取机会\r\n\r\n2、帮你吸引人脉\r\n\r\n一句话概括，个人品牌可以放大自己的势能，主动吸引人脉获取机会。", "top": 0, "createdAt": 1721142630, "style": "<style>.markdown-alert{padding:0.5rem 1rem;margin-bottom:1rem;border-left:.25em solid var(--borderColor-default,var(--color-border-default));}.markdown-alert .markdown-alert-title {display:flex;font-weight:var(--base-text-weight-medium,500);align-items:center;line-height:1;}.markdown-alert>:first-child {margin-top:0;}.markdown-alert>:last-child {margin-bottom:0;}</style><style>.markdown-alert.markdown-alert-important {border-left-color:var(--borderColor-done-emphasis, var(--color-done-emphasis));background-color:var(--color-done-subtle);}.markdown-alert.markdown-alert-important .markdown-alert-title {color: var(--fgColor-done,var(--color-done-fg));}</style>", "script": "<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>", "head": "", "ogImage": "https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG", "createdDate": "2024-07-16", "dateLabelColor": "#bc4c00"}, "P4": {"htmlDir": "docs/post/yan-shen-xun-lian.html", "labels": ["个人IP", "随笔"], "postTitle": "眼神训练", "postUrl": "post/yan-shen-xun-lian.html", "postSourceUrl": "https://github.com/hepingfly/hepingfly.github.io/issues/4", "commentNum": 0, "wordCount": 1285, "description": "友情提示：\r\n\r\n做这些动作训练眼部的时候，切记不要带隐形眼镜防止摩擦伤害眼睛。", "top": 0, "createdAt": 1721641813, "style": "<style>.markdown-alert{padding:0.5rem 1rem;margin-bottom:1rem;border-left:.25em solid var(--borderColor-default,var(--color-border-default));}.markdown-alert .markdown-alert-title {display:flex;font-weight:var(--base-text-weight-medium,500);align-items:center;line-height:1;}.markdown-alert>:first-child {margin-top:0;}.markdown-alert>:last-child {margin-bottom:0;}</style><style>.markdown-alert.markdown-alert-note {border-left-color:var(--borderColor-accent-emphasis, var(--color-accent-emphasis));background-color:var(--color-accent-subtle);}.markdown-alert.markdown-alert-note .markdown-alert-title {color: var(--fgColor-accent,var(--color-accent-fg));}</style>", "script": "<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>", "head": "", "ogImage": "https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG", "createdDate": "2024-07-22", "dateLabelColor": "#bc4c00"}, "P5": {"htmlDir": "docs/post/su-ren-du-shu-IP-ying-gai-zen-me-zuo-？.html", "labels": ["个人IP"], "postTitle": "素人读书IP应该怎么做？", "postUrl": "post/su-ren-du-shu-IP-ying-gai-zen-me-zuo-%EF%BC%9F.html", "postSourceUrl": "https://github.com/hepingfly/hepingfly.github.io/issues/5", "commentNum": 0, "wordCount": 1330, "description": "先提出一个问题：\r\n\r\n视频号上现在那么多情感书单账号，那么这些情感书单账号可以做历史方向的赛道吗？\r\n\r\n答案是：可以。", "top": 0, "createdAt": 1722715588, "style": "", "script": "<script src='https://hepingfly.github.io/donation.js'></script><script src='https://hepingfly.github.io/GmeekTOC.js'></script><script async src='https://www.googletagmanager.com/gtag/js?id=G-PB7Y2QXTLR'></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments);}gtag('js',new Date());gtag('config','G-PB7Y2QXTLR');</script>", "head": "", "ogImage": "https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG", "createdDate": "2024-08-04", "dateLabelColor": "#bc4c00"}}, "singeListJson": {}, "labelColorDict": {"bug": "#d73a4a", "documentation": "#0075ca", "duplicate": "#cfd3d7", "enhancement": "#a2eeef", "good first issue": "#7057ff", "help wanted": "#008672", "invalid": "#e4e669", "question": "#d876e3", "wontfix": "#ffffff", "个人IP": "#69A6B0", "随笔": "#35A1CD"}, "displayTitle": "和平自留地", "faviconUrl": "https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG", "ogImage": "https://shp-selfmedia-1257820375.cos.ap-shanghai.myqcloud.com/%E6%B2%88%E5%92%8C%E5%B9%B3%E5%A4%B4%E5%83%8F.PNG", "primerCSS": "<link href='https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/Primer/21.0.7/primer.css' rel='stylesheet' />", "homeUrl": "https://hepingfly.github.io", "prevUrl": "disabled", "nextUrl": "disabled"}